package com.example.pdd_nixinag;

import android.util.Log;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage.LoadPackageParam;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class PDDAdvancedHookModule implements IXposedHookLoadPackage {
    
    private static final String TAG = "PDDAdvancedHook";
    private static final String PDD_PACKAGE = "com.xunmeng.pinduoduo";
    
    @Override
    public void handleLoadPackage(LoadPackageParam lpparam) throws Throwable {
        if (!PDD_PACKAGE.equals(lpparam.packageName)) {
            return;
        }
        
        XposedBridge.log(TAG + ": 开始高级Hook拼多多应用");
        
        // Hook所有Activity的onCreate方法
        hookAllActivities(lpparam);
        
        // Hook所有Fragment的onViewCreated方法
        hookAllFragments(lpparam);
        
        // Hook RecyclerView的Adapter相关方法
        hookRecyclerViewAdapters(lpparam);
        
        // 暂时禁用网络Hook，避免闪退
        // hookNetworkClasses(lpparam);

        // 暂时禁用JSON Hook，避免闪退
        // hookJsonClasses(lpparam);

        LogUtils.i("⚠️ 网络Hook已暂时禁用，专注于稳定性");
        
        XposedBridge.log(TAG + ": 高级Hook注入完成");
    }
    
    /**
     * Hook所有Activity的onCreate方法
     */
    private void hookAllActivities(LoadPackageParam lpparam) {
        try {
            // Hook Android Activity基类
            Class<?> activityClass = XposedHelpers.findClass("android.app.Activity", lpparam.classLoader);
            
            XposedHelpers.findAndHookMethod(activityClass, "onCreate", 
                android.os.Bundle.class, new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        String className = param.thisObject.getClass().getName();
                        if (className.contains("pinduoduo")) {
                            XposedBridge.log(TAG + ": Activity创建: " + className);
                            
                            // 检查是否是商品详情相关的Activity
                            if (isGoodsRelatedClass(className)) {
                                XposedBridge.log(TAG + ": 🛍️ 检测到商品相关Activity: " + className);
                                analyzeObjectFields(param.thisObject, "商品Activity");
                            }
                        }
                    }
                }
            );
            
            XposedBridge.log(TAG + ": 成功Hook所有Activity");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook Activity失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook所有Fragment的onViewCreated方法
     */
    private void hookAllFragments(LoadPackageParam lpparam) {
        try {
            // Hook Fragment基类
            Class<?> fragmentClass = XposedHelpers.findClass("android.support.v4.app.Fragment", lpparam.classLoader);
            
            XposedHelpers.findAndHookMethod(fragmentClass, "onViewCreated", 
                android.view.View.class, android.os.Bundle.class, new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        String className = param.thisObject.getClass().getName();
                        if (className.contains("pinduoduo")) {
                            XposedBridge.log(TAG + ": Fragment视图创建: " + className);
                            
                            // 检查是否是商品或分类相关的Fragment
                            if (isGoodsRelatedClass(className) || isCategoryRelatedClass(className)) {
                                XposedBridge.log(TAG + ": 🛍️ 检测到商品/分类Fragment: " + className);
                                analyzeObjectFields(param.thisObject, "商品Fragment");
                                
                                // 延迟分析，等数据加载完成
                                android.os.Handler handler = new android.os.Handler();
                                handler.postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        analyzeObjectFields(param.thisObject, "延迟分析Fragment");
                                    }
                                }, 2000);
                            }
                        }
                    }
                }
            );
            
            XposedBridge.log(TAG + ": 成功Hook所有Fragment");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook Fragment失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook RecyclerView相关的Adapter
     */
    private void hookRecyclerViewAdapters(LoadPackageParam lpparam) {
        try {
            // 尝试多个可能的RecyclerView类路径
            Class<?> recyclerViewAdapterClass = null;
            Class<?> viewHolderClass = null;

            // 首先尝试AndroidX版本
            try {
                recyclerViewAdapterClass = XposedHelpers.findClass(
                    "androidx.recyclerview.widget.RecyclerView$Adapter", lpparam.classLoader);
                viewHolderClass = XposedHelpers.findClass(
                    "androidx.recyclerview.widget.RecyclerView$ViewHolder", lpparam.classLoader);
                LogUtils.i("找到AndroidX RecyclerView类");
            } catch (XposedHelpers.ClassNotFoundError e) {
                // 如果AndroidX不存在，尝试support库版本
                try {
                    recyclerViewAdapterClass = XposedHelpers.findClass(
                        "android.support.v7.widget.RecyclerView$Adapter", lpparam.classLoader);
                    viewHolderClass = XposedHelpers.findClass(
                        "android.support.v7.widget.RecyclerView$ViewHolder", lpparam.classLoader);
                    LogUtils.i("找到Support RecyclerView类");
                } catch (XposedHelpers.ClassNotFoundError e2) {
                    LogUtils.w("未找到RecyclerView类: " + e2.getMessage());
                    return;
                }
            }

            if (recyclerViewAdapterClass != null && viewHolderClass != null) {
                // Hook onBindViewHolder方法
                XposedHelpers.findAndHookMethod(recyclerViewAdapterClass, "onBindViewHolder",
                    viewHolderClass, int.class, new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        String adapterClassName = param.thisObject.getClass().getName();
                        if (adapterClassName.contains("pinduoduo") && 
                            (isGoodsRelatedClass(adapterClassName) || isCategoryRelatedClass(adapterClassName))) {
                            
                            int position = (Integer) param.args[1];
                            XposedBridge.log(TAG + ": 🛍️ RecyclerView绑定数据: " + adapterClassName + " position=" + position);
                            
                            // 分析ViewHolder和Adapter中的数据
                            Object viewHolder = param.args[0];
                            analyzeObjectFields(viewHolder, "ViewHolder[" + position + "]");
                            analyzeObjectFields(param.thisObject, "Adapter数据");
                        }
                    }
                });
            }

            XposedBridge.log(TAG + ": 成功Hook RecyclerView Adapter");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook RecyclerView Adapter失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook网络相关类 - 完全禁用，避免闪退
     */
    private void hookNetworkClasses(LoadPackageParam lpparam) {
        LogUtils.i("⚠️ 网络Hook已完全禁用，避免与混淆的OkHttp冲突");
        // 完全禁用所有网络Hook，避免与混淆的OkHttp API冲突
    }







    /**
     * Hook JSON解析类 - 完全禁用，避免冲突
     */
    private void hookJsonClasses(LoadPackageParam lpparam) {
        LogUtils.i("⚠️ JSON Hook已完全禁用，避免潜在冲突");
        // 完全禁用所有JSON Hook，专注于稳定性
    }




    
    /**
     * Hook指定类的指定方法（如果类存在）
     */
    private void hookClassIfExists(String className, LoadPackageParam lpparam, String[] methods) {
        try {
            Class<?> clazz = XposedHelpers.findClass(className, lpparam.classLoader);
            
            for (String methodName : methods) {
                try {
                    // 获取所有同名方法
                    Method[] allMethods = clazz.getDeclaredMethods();
                    for (Method method : allMethods) {
                        if (method.getName().equals(methodName)) {
                            XposedHelpers.findAndHookMethod(clazz, methodName, 
                                method.getParameterTypes(), new XC_MethodHook() {
                                    @Override
                                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                                        logMethodCall(className, methodName, param.args);
                                    }
                                    
                                    @Override
                                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                                        Object result = param.getResult();
                                        if (result != null && shouldAnalyzeResult(result, methodName)) {
                                            analyzeObjectFields(result, className + "." + methodName + "结果");
                                        }
                                    }
                                }
                            );
                            break; // Hook第一个匹配的方法
                        }
                    }
                } catch (Exception e) {
                    // 忽略单个方法Hook失败
                }
            }
        } catch (XposedHelpers.ClassNotFoundError e) {
            // 类不存在，忽略
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook类失败: " + className + " - " + e.getMessage());
        }
    }
    
    /**
     * 分析对象的所有字段
     */
    private void analyzeObjectFields(Object obj, String source) {
        if (obj == null) return;
        
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("\n").append("=".repeat(60)).append("\n");
            sb.append("🔍 对象分析: ").append(source).append("\n");
            sb.append("📦 类型: ").append(obj.getClass().getName()).append("\n");
            sb.append("⏰ 时间: ").append(new java.util.Date()).append("\n");
            sb.append("-".repeat(40)).append("\n");
            
            // 分析所有字段
            Field[] fields = obj.getClass().getDeclaredFields();
            int analyzedCount = 0;
            
            for (Field field : fields) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    
                    if (value != null && shouldAnalyzeField(field.getName(), value)) {
                        String fieldName = field.getName();
                        String valueStr = getValueDescription(value);
                        
                        sb.append("  📋 ").append(fieldName).append(": ").append(valueStr).append("\n");
                        analyzedCount++;
                        
                        // 如果字段值是List或数组，进一步分析
                        if (value instanceof java.util.List) {
                            analyzeListField((java.util.List<?>) value, fieldName);
                        }
                    }
                } catch (Exception e) {
                    // 忽略无法访问的字段
                }
            }
            
            sb.append("-".repeat(40)).append("\n");
            sb.append("📊 分析字段数: ").append(analyzedCount).append("/").append(fields.length).append("\n");
            sb.append("=".repeat(60)).append("\n");
            
            if (analyzedCount > 0) {
                XposedBridge.log(TAG + ": " + sb.toString());
                Log.d(TAG, sb.toString());
            }
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 分析对象失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析List字段
     */
    private void analyzeListField(java.util.List<?> list, String fieldName) {
        if (list == null || list.isEmpty()) return;
        
        try {
            XposedBridge.log(TAG + ": 📋 List字段分析: " + fieldName + " (size=" + list.size() + ")");
            
            for (int i = 0; i < Math.min(3, list.size()); i++) { // 只分析前3个元素
                Object item = list.get(i);
                if (item != null) {
                    String itemDesc = getValueDescription(item);
                    XposedBridge.log(TAG + ":   [" + i + "] " + itemDesc);
                    
                    // 如果是复杂对象，进一步分析
                    if (item.getClass().getName().contains("pinduoduo")) {
                        analyzeObjectFields(item, fieldName + "[" + i + "]");
                    }
                }
            }
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 分析List失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取值的描述
     */
    private String getValueDescription(Object value) {
        if (value == null) return "null";
        
        String valueStr = value.toString();
        String className = value.getClass().getSimpleName();
        
        // 截断过长的字符串
        if (valueStr.length() > 200) {
            valueStr = valueStr.substring(0, 200) + "...";
        }
        
        return className + "(" + valueStr + ")";
    }
    
    /**
     * 判断是否应该分析这个字段
     */
    private boolean shouldAnalyzeField(String fieldName, Object value) {
        if (value == null) return false;
        
        String lowerFieldName = fieldName.toLowerCase();
        String className = value.getClass().getName().toLowerCase();
        
        // 包含商品相关关键字的字段
        String[] goodsKeywords = {"goods", "product", "item", "sku", "price", "name", "title", 
                                 "image", "url", "desc", "detail", "category", "mall", "data", "list"};
        
        for (String keyword : goodsKeywords) {
            if (lowerFieldName.contains(keyword) || className.contains(keyword)) {
                return true;
            }
        }
        
        // List类型通常包含列表数据
        if (value instanceof java.util.List || value instanceof java.util.Map) {
            return true;
        }
        
        // 拼多多包名的类
        if (className.contains("pinduoduo")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 判断是否应该分析方法结果
     */
    private boolean shouldAnalyzeResult(Object result, String methodName) {
        if (result == null) return false;
        
        String lowerMethodName = methodName.toLowerCase();
        String[] keywords = {"json", "data", "response", "body", "goods", "product", "item"};
        
        for (String keyword : keywords) {
            if (lowerMethodName.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 判断是否是商品相关的类
     */
    private boolean isGoodsRelatedClass(String className) {
        String lowerClassName = className.toLowerCase();
        String[] keywords = {"goods", "product", "item", "sku", "detail", "gallery"};
        
        for (String keyword : keywords) {
            if (lowerClassName.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 判断是否是分类相关的类
     */
    private boolean isCategoryRelatedClass(String className) {
        String lowerClassName = className.toLowerCase();
        String[] keywords = {"category", "mall", "search", "result", "classification"};
        
        for (String keyword : keywords) {
            if (lowerClassName.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 记录方法调用
     */
    private void logMethodCall(String className, String methodName, Object[] args) {
        StringBuilder sb = new StringBuilder();
        sb.append("📞 方法调用: ").append(className).append(".").append(methodName);
        
        if (args != null && args.length > 0) {
            sb.append("(");
            for (int i = 0; i < Math.min(args.length, 3); i++) { // 只显示前3个参数
                if (i > 0) sb.append(", ");
                String argDesc = args[i] != null ? args[i].toString() : "null";
                if (argDesc.length() > 50) {
                    argDesc = argDesc.substring(0, 50) + "...";
                }
                sb.append(argDesc);
            }
            sb.append(")");
        } else {
            sb.append("()");
        }
        
        XposedBridge.log(TAG + ": " + sb.toString());
    }
}