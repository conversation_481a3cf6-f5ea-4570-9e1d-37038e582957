1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.pdd_nixinag"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.example.pdd_nixinag.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.example.pdd_nixinag.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:5:5-36:19
18        android:allowBackup="true"
18-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\adbd1bf8c0dde738d4ae689d2cff7ec7\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:12:9-35
28        android:theme="@style/Theme.Pdd_nixinag" >
28-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:13:9-49
29
30        <!-- 主Activity -->
31        <activity
31-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:16:9-23:20
32            android:name="com.example.pdd_nixinag.MainActivity"
32-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:17:13-41
33            android:exported="true" >
33-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:18:13-36
34            <intent-filter>
34-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:19:13-22:29
35                <action android:name="android.intent.action.MAIN" />
35-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:20:17-69
35-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:20:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:21:17-77
37-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:21:27-74
38            </intent-filter>
39        </activity>
40
41        <!-- Xposed模块元数据 -->
42        <meta-data
42-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:26:9-28:36
43            android:name="xposedmodule"
43-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:27:13-40
44            android:value="true" />
44-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:28:13-33
45        <meta-data
45-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:29:9-31:55
46            android:name="xposeddescription"
46-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:30:13-45
47            android:value="拼多多Hook模块 - 捕获商品详情和分类数据" />
47-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:31:13-52
48        <meta-data
48-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:32:9-34:34
49            android:name="xposedminversion"
49-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:33:13-44
50            android:value="54" />
50-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:34:13-31
51
52        <provider
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
53            android:name="androidx.startup.InitializationProvider"
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
54            android:authorities="com.example.pdd_nixinag.androidx-startup"
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
55            android:exported="false" >
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
56            <meta-data
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.emoji2.text.EmojiCompatInitializer"
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
58                android:value="androidx.startup" />
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60ad9587181d9ac47b17af7cceba718d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
59            <meta-data
59-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd49396ca2dafe9ce896b76ae0dc7821\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
60-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd49396ca2dafe9ce896b76ae0dc7821\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
61                android:value="androidx.startup" />
61-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd49396ca2dafe9ce896b76ae0dc7821\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
64                android:value="androidx.startup" />
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
65        </provider>
66
67        <receiver
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
68            android:name="androidx.profileinstaller.ProfileInstallReceiver"
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
69            android:directBootAware="false"
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
70            android:enabled="true"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
71            android:exported="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
72            android:permission="android.permission.DUMP" >
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
74                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
77                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
80                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
83                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46c413aa473bd610eb578a8c7d39d8f2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
84            </intent-filter>
85        </receiver>
86    </application>
87
88</manifest>
