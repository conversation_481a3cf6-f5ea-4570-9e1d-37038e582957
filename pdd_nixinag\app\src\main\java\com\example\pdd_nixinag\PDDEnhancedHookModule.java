package com.example.pdd_nixinag;

import android.util.Log;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage.LoadPackageParam;
import java.lang.reflect.Method;
import java.util.Arrays;

public class PDDEnhancedHookModule implements IXposedHookLoadPackage {
    
    private static final String TAG = "PDDEnhancedHook";
    private static final String PDD_PACKAGE = "com.xunmeng.pinduoduo";
    
    /**
     * 安全地查找类，如果不存在则返回null
     */
    private Class<?> findClassIfExists(String className, ClassLoader classLoader) {
        try {
            return XposedHelpers.findClass(className, classLoader);
        } catch (XposedHelpers.ClassNotFoundError e) {
            return null;
        } catch (Exception e) {
            return null;
        }
    }
    
    @Override
    public void handleLoadPackage(LoadPackageParam lpparam) throws Throwable {
        if (!PDD_PACKAGE.equals(lpparam.packageName)) {
            return;
        }
        
        XposedBridge.log(TAG + ": 开始增强Hook拼多多应用");
        
        // Hook网络请求相关
        hookHttpRequests(lpparam);
        
        // Hook JSON解析相关
        hookJsonParsing(lpparam);
        
        // Hook商品相关核心方法
        hookProductCore(lpparam);
        
        // Hook分类相关方法
        hookCategoryCore(lpparam);
        
        // Hook数据提供者
        hookDataProviders(lpparam);
        
        XposedBridge.log(TAG + ": 增强Hook注入完成");
    }
    
    /**
     * Hook HTTP请求相关方法，捕获网络数据
     */
    private void hookHttpRequests(LoadPackageParam lpparam) {
        try {
            // Hook OkHttp相关请求
            Class<?> requestClass = findClassIfExists("okhttp3.Request", lpparam.classLoader);
            if (requestClass != null) {
                XposedHelpers.findAndHookMethod(requestClass, "url", new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        String url = param.getResult().toString();
                        if (isProductOrCategoryUrl(url)) {
                            XposedBridge.log(TAG + ": 捕获商品/分类请求URL: " + url);
                            Log.d(TAG, "捕获商品/分类请求URL: " + url);
                        }
                    }
                });
            }
            
            // Hook Response处理
            Class<?> responseClass = findClassIfExists("okhttp3.Response", lpparam.classLoader);
            if (responseClass != null) {
                XposedHelpers.findAndHookMethod(responseClass, "body", new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        try {
                            Object responseBody = param.getResult();
                            if (responseBody != null) {
                                // 这里可以进一步解析response body
                                XposedBridge.log(TAG + ": 捕获到响应数据");
                            }
                        } catch (Exception e) {
                            // 忽略错误，避免影响正常流程
                        }
                    }
                });
            }
            
            XposedBridge.log(TAG + ": 成功Hook HTTP请求");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook HTTP请求失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook JSON解析相关方法
     */
    private void hookJsonParsing(LoadPackageParam lpparam) {
        try {
            // Hook Gson解析
            Class<?> gsonClass = findClassIfExists("com.google.gson.Gson", lpparam.classLoader);
            if (gsonClass != null) {
                XposedHelpers.findAndHookMethod(gsonClass, "fromJson", String.class, Class.class, new XC_MethodHook() {
                    @Override
                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                        String json = (String) param.args[0];
                        Class<?> clazz = (Class<?>) param.args[1];
                        
                        if (isProductOrCategoryJson(json, clazz)) {
                            XposedBridge.log(TAG + ": 捕获JSON解析 - 类型: " + clazz.getSimpleName());
                            XposedBridge.log(TAG + ": JSON数据: " + truncateString(json, 500));
                            Log.d(TAG, "JSON解析 - 类型: " + clazz.getSimpleName() + ", 数据: " + truncateString(json, 200));
                        }
                    }
                    
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        Object result = param.getResult();
                        if (result != null && isProductOrCategoryObject(result)) {
                            logObjectData(result, "JSON解析结果");
                        }
                    }
                });
            }
            
            XposedBridge.log(TAG + ": 成功Hook JSON解析");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook JSON解析失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook商品相关核心方法
     */
    private void hookProductCore(LoadPackageParam lpparam) {
        try {
            // Hook商品数据提供者
            Class<?> goodsDataProviderClass = findClassIfExists(
                "com.xunmeng.pinduoduo.goods.model.GoodsDetailSkuDataProvider", 
                lpparam.classLoader
            );
            
            if (goodsDataProviderClass != null) {
                hookAllMethods(goodsDataProviderClass, "GoodsDetailSkuDataProvider", true);
            }
            
            // Hook商品实体相关类
            hookClassByName(lpparam, "com.xunmeng.pinduoduo.goods.entity.GoodsEntity", "GoodsEntity");
            hookClassByName(lpparam, "com.xunmeng.pinduoduo.goods.entity.C9249f1", "GoodsEntityF1");
            hookClassByName(lpparam, "com.xunmeng.pinduoduo.goods.entity.C9234a1", "GoodsEntityA1");
            
            XposedBridge.log(TAG + ": 成功Hook商品核心方法");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook商品核心方法失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook分类相关方法
     */
    private void hookCategoryCore(LoadPackageParam lpparam) {
        try {
            // Hook商城相关的分类数据
            hookClassByPattern(lpparam, "category", "分类相关");
            hookClassByPattern(lpparam, "Category", "分类相关");
            hookClassByPattern(lpparam, "mall", "商城相关");
            
            XposedBridge.log(TAG + ": 成功Hook分类核心方法");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook分类核心方法失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook数据提供者
     */
    private void hookDataProviders(LoadPackageParam lpparam) {
        try {
            // Hook所有可能的数据提供者
            hookClassByPattern(lpparam, "DataProvider", "数据提供者");
            hookClassByPattern(lpparam, "Provider", "提供者");
            hookClassByPattern(lpparam, "Repository", "仓库");
            hookClassByPattern(lpparam, "Service", "服务");
            
            XposedBridge.log(TAG + ": 成功Hook数据提供者");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook数据提供者失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据类名Hook类
     */
    private void hookClassByName(LoadPackageParam lpparam, String className, String displayName) {
        try {
            Class<?> clazz = findClassIfExists(className, lpparam.classLoader);
            if (clazz != null) {
                hookAllMethods(clazz, displayName, false);
                XposedBridge.log(TAG + ": 成功Hook类: " + displayName);
            }
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook类失败: " + displayName + " - " + e.getMessage());
        }
    }
    
    /**
     * 根据模式Hook类（通过反射查找包含指定字符串的类）
     */
    private void hookClassByPattern(LoadPackageParam lpparam, String pattern, String displayName) {
        try {
            // 这里需要遍历类加载器中的类，实际实现会比较复杂
            // 简化版本：直接尝试一些常见的类名
            String[] possibleClasses = {
                "com.xunmeng.pinduoduo.mall." + pattern,
                "com.xunmeng.pinduoduo.category." + pattern,
                "com.xunmeng.pinduoduo.goods." + pattern
            };
            
            for (String className : possibleClasses) {
                hookClassByName(lpparam, className, displayName + "(" + className + ")");
            }
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook模式类失败: " + pattern + " - " + e.getMessage());
        }
    }
    
    /**
     * Hook一个类的所有方法
     */
    private void hookAllMethods(Class<?> clazz, String className, boolean onlyPublic) {
        Method[] methods = onlyPublic ? clazz.getMethods() : clazz.getDeclaredMethods();
        int hookedCount = 0;
        
        for (Method method : methods) {
            try {
                if (shouldHookMethod(method)) {
                    XposedHelpers.findAndHookMethod(clazz, method.getName(), 
                        method.getParameterTypes(), new XC_MethodHook() {
                            @Override
                            protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                                String args = Arrays.toString(param.args);
                                XposedBridge.log(TAG + ": 调用方法 " + className + "." + method.getName() + "(" + truncateString(args, 100) + ")");
                                Log.d(TAG, "调用方法 " + className + "." + method.getName() + "(" + truncateString(args, 100) + ")");
                            }
                            
                            @Override
                            protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                                Object result = param.getResult();
                                if (result != null && isProductOrCategoryObject(result)) {
                                    logObjectData(result, className + "." + method.getName() + " 返回值");
                                }
                            }
                        }
                    );
                    hookedCount++;
                }
            } catch (Exception e) {
                // 某些方法可能Hook失败，继续处理其他方法
            }
        }
        
        XposedBridge.log(TAG + ": 在" + className + "中Hook了" + hookedCount + "个方法");
    }
    
    /**
     * 判断是否应该Hook这个方法
     */
    private boolean shouldHookMethod(Method method) {
        String methodName = method.getName();
        return !methodName.equals("hashCode") && 
               !methodName.equals("equals") && 
               !methodName.equals("toString") && 
               !methodName.startsWith("get") ||
               methodName.startsWith("set") ||
               methodName.contains("product") ||
               methodName.contains("goods") ||
               methodName.contains("category") ||
               methodName.contains("data");
    }
    
    /**
     * 判断URL是否与商品或分类相关
     */
    private boolean isProductOrCategoryUrl(String url) {
        if (url == null) return false;
        String lowerUrl = url.toLowerCase();
        return lowerUrl.contains("product") || 
               lowerUrl.contains("goods") || 
               lowerUrl.contains("item") || 
               lowerUrl.contains("category") || 
               lowerUrl.contains("mall") ||
               lowerUrl.contains("detail");
    }
    
    /**
     * 判断JSON是否与商品或分类相关
     */
    private boolean isProductOrCategoryJson(String json, Class<?> clazz) {
        if (json == null || clazz == null) return false;
        
        String jsonLower = json.toLowerCase();
        String className = clazz.getSimpleName().toLowerCase();
        
        return jsonLower.contains("product") || 
               jsonLower.contains("goods") || 
               jsonLower.contains("item") || 
               jsonLower.contains("category") ||
               className.contains("goods") ||
               className.contains("product") ||
               className.contains("category");
    }
    
    /**
     * 判断对象是否与商品或分类相关
     */
    private boolean isProductOrCategoryObject(Object obj) {
        if (obj == null) return false;
        String className = obj.getClass().getSimpleName().toLowerCase();
        return className.contains("goods") || 
               className.contains("product") || 
               className.contains("item") || 
               className.contains("category") ||
               className.contains("mall");
    }
    
    /**
     * 记录对象数据
     */
    private void logObjectData(Object obj, String source) {
        if (obj == null) return;
        
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("\n========== ").append(source).append(" ==========\n");
            sb.append("对象类型: ").append(obj.getClass().getName()).append("\n");
            
            // 使用反射获取所有字段
            java.lang.reflect.Field[] fields = obj.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    if (value != null) {
                        String valueStr = truncateString(value.toString(), 200);
                        sb.append(field.getName()).append(": ").append(valueStr).append("\n");
                    }
                } catch (Exception e) {
                    // 忽略无法访问的字段
                }
            }
            
            sb.append("==================== END ===================\n");
            
            String logMessage = sb.toString();
            XposedBridge.log(TAG + ": " + logMessage);
            Log.d(TAG, logMessage);
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 记录对象数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 截断字符串到指定长度
     */
    private String truncateString(String str, int maxLength) {
        if (str == null) return "null";
        if (str.length() <= maxLength) return str;
        return str.substring(0, maxLength) + "...";
    }
}