09-02 14:10:13.341  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.342  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1"}
09-02 14:10:13.344  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.344  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https://img.pddpic.com/mms-material-img/2025-05-16/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https://img.pddpic.com/mms-material-img/2025-05-16/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2/format/pdic/decver/4/quality/90/thumbnail/400x","page_from":"35"}
09-02 14:10:13.346  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.346  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"address_list":[],"page_sn":"10014","page_id":"10014_1756793413343_0262992795","goods_id":"************","phone_model":"53098853518BBC54E5BA","page_from":"35","page_version":"7","client_time":"1756793413344","refer_page_sn":"10002","refer_page_el_sn":"99862","pic_w":0,"pic_h":0,"has_pic_url":1,"extend_map":{},"_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","union_pay_installed":false,"client_lab... (截断)
09-02 14:10:13.349  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.349  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35"}
09-02 14:10:13.350  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.350  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35"}
09-02 14:10:13.350  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.350  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35"}
09-02 14:10:13.354  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.354  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.356  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.356  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.358  3951  4105 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.358  3951  4105 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.358  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.359  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.363  3951  4095 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: g
09-02 14:10:13.363  3951  4095 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: [{"path":"live_activity_popup_618.html","mini_version_code":28000},{"path":"complaint.html","mini_version_code":28000},{"path":"complaint_detail.html","mini_version_code":28000},{"path":"order_checkout.html","mini_version_code":28000},{"path":"complaint_list.html","mini_version_code":28000},{"path":"deposit.html","mini_version_code":28000},{"path":"group7.html","mini_version_code":28000},{"path":"refund_trace.html","mini_version_code":28000},{"path":"lottery_refund_track.html","mini_version_code":28000},{"path":"self_service.html","mini_version_code":28000},{"path":"complaint_mall.html","mini_version_code":28000},{"path":"questions.html","mini_version_code":28000},{"path":"sub_questions.html","mini_version_code":28000},{"path":"solutions.html","mini_version_code":28000},{"path":"complaints... (截断)
09-02 14:10:13.365  3951  4095 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: com.google.gson.g
09-02 14:10:13.365  3951  4095 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: [{"path":"live_activity_popup_618.html","mini_version_code":28000},{"path":"complaint.html","mini_version_code":28000},{"path":"complaint_detail.html","mini_version_code":28000},{"path":"order_checkout.html","mini_version_code":28000},{"path":"complaint_list.html","mini_version_code":28000},{"path":"deposit.html","mini_version_code":28000},{"path":"group7.html","mini_version_code":28000},{"path":"refund_trace.html","mini_version_code":28000},{"path":"lottery_refund_track.html","mini_version_code":28000},{"path":"self_service.html","mini_version_code":28000},{"path":"complaint_mall.html","mini_version_code":28000},{"path":"questions.html","mini_version_code":28000},{"path":"sub_questions.html","mini_version_code":28000},{"path":"solutions.html","mini_version_code":28000},{"path":"complaints... (截断)
09-02 14:10:13.376  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.376  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {
09-02 14:10:13.376  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.376  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.376  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.376  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {
09-02 14:10:13.377  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.377  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {
09-02 14:10:13.416  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.416  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.420  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.420  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.422  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.422  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.427  4040  4040 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.427  4040  4040 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"type":1,"page_stack":"{\"activityName\":\"NewPageActivity\",\"createTime\":17832958,\"finished\":false,\"hideTime\":0,\"pageMask\":false,\"page_hash\":83623188,\"page_type\":\"pdd_goods_detail\",\"page_url\":\"goods.html?goods_id\\u003d************\\u0026_oak_rcto\\u003dYWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM\\u0026_oak_gallery_token\\u003d64038bdf69bb7127251f2f0c7a66a657\\u0026_oak_gallery\\u003dhttps%3A%2F%2Fimg.pddpic.com%2Fmms-material-img%2F2025-05-16%2F9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg\\u0026pr_force_native\\u003d1\"}"}
09-02 14:10:13.428  4040  4040 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: PageStack
09-02 14:10:13.428  4040  4040 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"activityName":"NewPageActivity","createTime":17832958,"finished":false,"hideTime":0,"pageMask":false,"page_hash":83623188,"page_type":"pdd_goods_detail","page_url":"goods.html?goods_id\u003d************\u0026_oak_rcto\u003dYWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM\u0026_oak_gallery_token\u003d64038bdf69bb7127251f2f0c7a66a657\u0026_oak_gallery\u003dhttps%3A%2F%2Fimg.pddpic.com%2Fmms-material-img%2F2025-05-16%2F9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg\u0026pr_force_native\u003d1"}
09-02 14:10:13.429  4040  4040 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: com.xunmeng.pinduoduo.api_router.entity.PageStack
09-02 14:10:13.429  4040  4040 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"activityName":"NewPageActivity","createTime":17832958,"finished":false,"hideTime":0,"pageMask":false,"page_hash":83623188,"page_type":"pdd_goods_detail","page_url":"goods.html?goods_id\u003d************\u0026_oak_rcto\u003dYWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM\u0026_oak_gallery_token\u003d64038bdf69bb7127251f2f0c7a66a657\u0026_oak_gallery\u003dhttps%3A%2F%2Fimg.pddpic.com%2Fmms-material-img%2F2025-05-16%2F9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg\u0026pr_force_native\u003d1"}
09-02 14:10:13.429  4040  4040 I XposedBridge: PDDJsonDataHook: ✅ Gson解析结果: PageStack
09-02 14:10:13.430  4040  4040 I XposedBridge: PDDJsonDataHook:
09-02 14:10:13.430  4197  4197 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.430  4197  4197 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"type":1,"page_stack":"{\"activityName\":\"NewPageActivity\",\"createTime\":17832958,\"finished\":false,\"hideTime\":0,\"pageMask\":false,\"page_hash\":83623188,\"page_type\":\"pdd_goods_detail\",\"page_url\":\"goods.html?goods_id\\u003d************\\u0026_oak_rcto\\u003dYWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM\\u0026_oak_gallery_token\\u003d64038bdf69bb7127251f2f0c7a66a657\\u0026_oak_gallery\\u003dhttps%3A%2F%2Fimg.pddpic.com%2Fmms-material-img%2F2025-05-16%2F9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg\\u0026pr_force_native\\u003d1\"}"}
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook: 🎯========== Gson解析结果 ==========
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook: 📦 对象类型: com.xunmeng.pinduoduo.api_router.entity.PageStack
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 activityName: String(NewPageActivity)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 createTime: Long(17832958)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 finished: Boolean(false)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 hideTime: Long(0)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 pageMask: Boolean(false)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 page_hash: Integer(83623188)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 page_type: String(pdd_goods_detail)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 page_url: String(goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM&...)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_HASH: Integer(3)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_ID: Integer(1)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_MASK: Integer(7)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_ROUTE_PATH: Integer(5)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_SN: Integer(6)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_TITLE: Integer(2)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_URL: Integer(4)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook:   ... (更多字段已省略)
09-02 14:10:13.431  4040  4040 D PDDJsonDataHook: ==========================================
09-02 14:10:13.433  4479  4479 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.433  4479  4479 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"type":1,"page_stack":"{\"activityName\":\"NewPageActivity\",\"createTime\":17832958,\"finished\":false,\"hideTime\":0,\"pageMask\":false,\"page_hash\":83623188,\"page_type\":\"pdd_goods_detail\",\"page_url\":\"goods.html?goods_id\\u003d************\\u0026_oak_rcto\\u003dYWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM\\u0026_oak_gallery_token\\u003d64038bdf69bb7127251f2f0c7a66a657\\u0026_oak_gallery\\u003dhttps%3A%2F%2Fimg.pddpic.com%2Fmms-material-img%2F2025-05-16%2F9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg\\u0026pr_force_native\\u003d1\"}"}
09-02 14:10:13.452  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.452  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"address_list":[],"page_sn":"10014","page_id":"10014_1756793413343_0262992795","goods_id":"************","phone_model":"53098853518BBC54E5BA","page_from":"35","page_version":"7","client_time":"1756793413450","refer_page_sn":"10002","refer_page_el_sn":"99862","pic_w":0,"pic_h":0,"has_pic_url":1,"extend_map":{},"_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","union_pay_installed":false,"client_lab... (截断)
09-02 14:10:13.453  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.453  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https://img.pddpic.com/mms-material-img/2025-05-16/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https://img.pddpic.com/mms-material-img/2025-05-16/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2/format/pdic/decver/4/quality/90/thumbnail/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM&_oak_galler... (截断)
09-02 14:10:13.458  4040  4040 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.458  4040  4040 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"type":3,"page_stack":"{\"activityName\":\"NewPageActivity\",\"createTime\":17832958,\"finished\":false,\"hideTime\":0,\"pageMask\":false,\"pageSn\":\"10014\",\"page_hash\":83623188,\"page_id\":\"10014_1756793413381_1079535609\",\"page_type\":\"pdd_goods_detail\",\"page_url\":\"goods.html?goods_id\\u003d************\\u0026_oak_rcto\\u003dYWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM\\u0026_oak_gallery_token\\u003d64038bdf69bb7127251f2f0c7a66a657\\u0026_oak_gallery\\u003dhttps%3A%2F%2Fimg.pddpic.com%2Fmms-material-img%2F2025-05-16%2F9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg\\... (截断)
09-02 14:10:13.459  4040  4040 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: PageStack
09-02 14:10:13.459  4040  4040 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"activityName":"NewPageActivity","createTime":17832958,"finished":false,"hideTime":0,"pageMask":false,"pageSn":"10014","page_hash":83623188,"page_id":"10014_1756793413381_1079535609","page_type":"pdd_goods_detail","page_url":"goods.html?goods_id\u003d************\u0026_oak_rcto\u003dYWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM\u0026_oak_gallery_token\u003d64038bdf69bb7127251f2f0c7a66a657\u0026_oak_gallery\u003dhttps%3A%2F%2Fimg.pddpic.com%2Fmms-material-img%2F2025-05-16%2F9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg\u0026pr_force_native\u003d1"}
09-02 14:10:13.459  4040  4040 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: com.xunmeng.pinduoduo.api_router.entity.PageStack
09-02 14:10:13.459  4040  4040 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"activityName":"NewPageActivity","createTime":17832958,"finished":false,"hideTime":0,"pageMask":false,"pageSn":"10014","page_hash":83623188,"page_id":"10014_1756793413381_1079535609","page_type":"pdd_goods_detail","page_url":"goods.html?goods_id\u003d************\u0026_oak_rcto\u003dYWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM\u0026_oak_gallery_token\u003d64038bdf69bb7127251f2f0c7a66a657\u0026_oak_gallery\u003dhttps%3A%2F%2Fimg.pddpic.com%2Fmms-material-img%2F2025-05-16%2F9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg\u0026pr_force_native\u003d1"}
09-02 14:10:13.460  4040  4040 I XposedBridge: PDDJsonDataHook: ✅ Gson解析结果: PageStack
09-02 14:10:13.460  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.460  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.460  4040  4040 I XposedBridge: PDDJsonDataHook:
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook: 🎯========== Gson解析结果 ==========
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook: 📦 对象类型: com.xunmeng.pinduoduo.api_router.entity.PageStack
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 activityName: String(NewPageActivity)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 createTime: Long(17832958)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 finished: Boolean(false)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 hideTime: Long(0)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 pageMask: Boolean(false)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 pageSn: String(10014)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 page_hash: Integer(83623188)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 page_id: String(10014_1756793413381_1079535609)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 page_type: String(pdd_goods_detail)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 page_url: String(goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM&...)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_HASH: Integer(3)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_ID: Integer(1)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_MASK: Integer(7)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_ROUTE_PATH: Integer(5)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   📋 KEY_PAGE_SN: Integer(6)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook:   ... (更多字段已省略)
09-02 14:10:13.460  4040  4040 D PDDJsonDataHook: ==========================================
09-02 14:10:13.461  4197  4197 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.461  4197  4197 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"type":3,"page_stack":"{\"activityName\":\"NewPageActivity\",\"createTime\":17832958,\"finished\":false,\"hideTime\":0,\"pageMask\":false,\"pageSn\":\"10014\",\"page_hash\":83623188,\"page_id\":\"10014_1756793413381_1079535609\",\"page_type\":\"pdd_goods_detail\",\"page_url\":\"goods.html?goods_id\\u003d************\\u0026_oak_rcto\\u003dYWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM\\u0026_oak_gallery_token\\u003d64038bdf69bb7127251f2f0c7a66a657\\u0026_oak_gallery\\u003dhttps%3A%2F%2Fimg.pddpic.com%2Fmms-material-img%2F2025-05-16%2F9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg\\... (截断)
09-02 14:10:13.462  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.462  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.463  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.463  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.463  4479  4479 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.463  4479  4479 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"type":3,"page_stack":"{\"activityName\":\"NewPageActivity\",\"createTime\":17832958,\"finished\":false,\"hideTime\":0,\"pageMask\":false,\"pageSn\":\"10014\",\"page_hash\":83623188,\"page_id\":\"10014_1756793413381_1079535609\",\"page_type\":\"pdd_goods_detail\",\"page_url\":\"goods.html?goods_id\\u003d************\\u0026_oak_rcto\\u003dYWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM\\u0026_oak_gallery_token\\u003d64038bdf69bb7127251f2f0c7a66a657\\u0026_oak_gallery\\u003dhttps%3A%2F%2Fimg.pddpic.com%2Fmms-material-img%2F2025-05-16%2F9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg\\... (截断)
09-02 14:10:13.522  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.522  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https://img.pddpic.com/mms-material-img/2025-05-16/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https://img.pddpic.com/mms-material-img/2025-05-16/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2/format/pdic/decver/4/quality/90/thumbnail/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM&_oak_galler... (截断)
09-02 14:10:13.568  3951  4190 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: com.xunmeng.pinduoduo.goods.entity.IntegrationRenderResponse
09-02 14:10:13.568  3951  4190 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"server_time":1756793414,"destination_url":"order_checkout.html","destination_type":2,"pre_render_url":"csr/comm_mall_home_pre_render.html","goods":{"goods_id":************,"cat_id":9662,"mall_id":153640912,"market_price":1012,"quantity":1000,"allowed_region":"2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32","country":"","warehouse":"","goods_type":1,"cost_template_id":544142245494784,"vas_template_id":{},"cost_province_codes":"5,9,19,20,21,28,29","has_cost":0,"customer_num":2,"options":[0,37,7,9,60,13],"new_options":[0,37,230,7,200,9,13,117,216,122,123,187,188,60],"goods_name":"碳晶板无缝墙板金属板护墙板集成墙板竹木纤维木饰面板金刚 防撞板","goods_desc":"碳晶板无缝墙板金属板护墙板集成墙板竹木纤维木饰面板金刚防撞板","share_desc":"【退货包运费】碳晶板无缝墙板金属板护墙板集成墙板竹木纤维木饰面板金刚防撞板","share_link":"goods2.html?goods_id=8044591579... (截断)
09-02 14:10:13.576  3951  4102 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.576  3951  4102 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"server_time":1756793414,"destination_url":"order_checkout.html","destination_type":2,"pre_render_url":"csr/comm_mall_home_pre_render.html","goods":{"goods_id":************,"cat_id":9662,"mall_id":153640912,"market_price":1012,"quantity":1000,"allowed_region":"2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32","country":"","warehouse":"","goods_type":1,"cost_template_id":544142245494784,"vas_template_id":{},"cost_province_codes":"5,9,19,20,21,28,29","has_cost":0,"customer_num":2,"options":[0,37,7,9,60,13],"new_options":[0,37,230,7,200,9,13,117,216,122,123,18... (截断)
09-02 14:10:13.624  3951  4095 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: ReplayWindowInfo
09-02 14:10:13.624  3951  4095 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"width":360,"height":640,"floatWindowAddBorder":false,"recommendOnewayRequestError":false,"feed_id":"6691276866799064539","mall_id":null,"event_id":3156097321,"event_feed_id":"6691276866799064539","show_id":"6691276866799064539","total_pv":null,"link_url":"live_room.html?ext=%7B%22feed_scene_id%22%3A651%7D&show_red_box=0&promotion_ui_style=2&biz_type=1&scene_id=50&goods_id=************&use_hub=1&feed_id=6691276866799064539&page_from=601109&sub_biz_type=101&container_hub_type=hub%2Fzb_promotions_scene%2Fweak&head_ids=6691276866799064539&hub_type=hub%2Fzb_promotions_scene%2Fweak&video_from_link=goods.html%3Fgoods_id%3D************&feed_owner_type=1&location_required=0&goods_cat_id2=9362","video_url":null,"duration_millis":null,"goods_id":************,"is_fake_customer_mode":false,"cover_url... (截断)
09-02 14:10:13.625  3951  4095 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: com.xunmeng.pdd_av_foundation.pddlivescene.float_window.replay.ReplayWindowInfo
09-02 14:10:13.625  3951  4095 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"width":360,"height":640,"floatWindowAddBorder":false,"recommendOnewayRequestError":false,"feed_id":"6691276866799064539","mall_id":null,"event_id":3156097321,"event_feed_id":"6691276866799064539","show_id":"6691276866799064539","total_pv":null,"link_url":"live_room.html?ext=%7B%22feed_scene_id%22%3A651%7D&show_red_box=0&promotion_ui_style=2&biz_type=1&scene_id=50&goods_id=************&use_hub=1&feed_id=6691276866799064539&page_from=601109&sub_biz_type=101&container_hub_type=hub%2Fzb_promotions_scene%2Fweak&head_ids=6691276866799064539&hub_type=hub%2Fzb_promotions_scene%2Fweak&video_from_link=goods.html%3Fgoods_id%3D************&feed_owner_type=1&location_required=0&goods_cat_id2=9362","video_url":null,"duration_millis":null,"goods_id":************,"is_fake_customer_mode":false,"cover_url... (截断)
09-02 14:10:13.682  3951  4190 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: HttpError
09-02 14:10:13.682  3951  4190 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"success":true,"error_code":1000000,"result":{"goods_id":************,"uid":9268660797464,"is_fav":false,"server_time":1756793414}}
09-02 14:10:13.682  3951  4190 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: com.xunmeng.pinduoduo.basekit.http.entity.HttpError
09-02 14:10:13.682  3951  4190 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"success":true,"error_code":1000000,"result":{"goods_id":************,"uid":9268660797464,"is_fav":false,"server_time":1756793414}}
09-02 14:10:13.682  3951  4190 I XposedBridge: PDDJsonDataHook: ✅ Gson解析结果: HttpError
09-02 14:10:13.682  3951  4190 I XposedBridge: PDDJsonDataHook:
09-02 14:10:13.682  3951  4190 D PDDJsonDataHook: 🎯========== Gson解析结果 ==========
09-02 14:10:13.682  3951  4190 D PDDJsonDataHook: 📦 对象类型: com.xunmeng.pinduoduo.basekit.http.entity.HttpError
09-02 14:10:13.682  3951  4190 D PDDJsonDataHook:   📋 error_code: Integer(1000000)
09-02 14:10:13.682  3951  4190 D PDDJsonDataHook: ==========================================
09-02 14:10:13.683  3951  4190 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: HttpError
09-02 14:10:13.683  3951  4190 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"success":true,"error_code":1000000,"result":{"goods_id":************,"uid":9268660797464,"is_fav":false,"server_time":1756793414}}
09-02 14:10:13.683  3951  4190 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: com.xunmeng.pinduoduo.basekit.http.entity.HttpError
09-02 14:10:13.683  3951  4190 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"success":true,"error_code":1000000,"result":{"goods_id":************,"uid":9268660797464,"is_fav":false,"server_time":1756793414}}
09-02 14:10:13.684  3951  4190 I XposedBridge: PDDJsonDataHook: ✅ Gson解析结果: HttpError
09-02 14:10:13.684  3951  4190 I XposedBridge: PDDJsonDataHook:
09-02 14:10:13.684  3951  4190 D PDDJsonDataHook: 🎯========== Gson解析结果 ==========
09-02 14:10:13.684  3951  4190 D PDDJsonDataHook: 📦 对象类型: com.xunmeng.pinduoduo.basekit.http.entity.HttpError
09-02 14:10:13.684  3951  4190 D PDDJsonDataHook:   📋 error_code: Integer(1000000)
09-02 14:10:13.684  3951  4190 D PDDJsonDataHook: ==========================================
09-02 14:10:13.794  3951  4105 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.794  3951  4105 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"10001":"/api/aquarius/hungary/global/personal_center","10002":"/api/aquarius/hungary/global/homepage","10004":"/api/aquarius/hungary/global/order","10014":"/api/aquarius/hungary/global/goods_details","10015":"/api/aquarius/hungary/global/search","10025":"/api/aquarius/hungary/global/special","10032":"/api/aquarius/hungary/global/order_list","10051":"/api/aquarius/hungary/global/chat","10104":"/api/aquarius/hungary/global/pxq","23009":"/api/aquarius/hungary/global/special","53685":"/api/aquarius/hungary/global/special","84134":"/api/aquarius/hungary/global/special","92009":"/api/aquarius/hung... (截断)
09-02 14:10:13.795  3951  4105 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: java.util.HashMap<java.lang.String, java.lang.String>
09-02 14:10:13.795  3951  4105 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"10001":"\/api\/aquarius\/hungary\/global\/personal_center","10002":"\/api\/aquarius\/hungary\/global\/homepage","10004":"\/api\/aquarius\/hungary\/global\/order","10014":"\/api\/aquarius\/hungary\/global\/goods_details","10015":"\/api\/aquarius\/hungary\/global\/search","10025":"\/api\/aquarius\/hungary\/global\/special","10032":"\/api\/aquarius\/hungary\/global\/order_list","10051":"\/api\/aquarius\/hungary\/global\/chat","10104":"\/api\/aquarius\/hungary\/global\/pxq","23009":"\/api\/aquarius\/hungary\/global\/special","53685":"\/api\/aquarius\/hungary\/global\/special","84134":"\/api\/aquarius\/hungary\/global\/special","92009":"\/api\/aquarius\/hungary\/global\/live_tab","-10001":"\/api\/aquarius\/hungary\/global\/app_popup"}
09-02 14:10:13.802  3951  4105 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:13.803  3951  4105 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"goods_id":"************","_oak_rcto":"YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM","_oak_gallery_token":"64038bdf69bb7127251f2f0c7a66a657","_oak_gallery":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg","pr_force_native":"1","thumb_url":"https:\/\/img.pddpic.com\/mms-material-img\/2025-05-16\/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg?imageMogr2\/format\/pdic\/decver\/4\/quality\/90\/thumbnail\/400x","page_from":"35","url":"goods.html?goods_id=************&_oak_rcto=YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgIS... (截断)
09-02 14:10:13.902  3951  4191 I XposedBridge: PDDJsonDataHook: 🎯 Gson解析商品JSON - 类型: com.xunmeng.pinduoduo.popup.network.PopupResponse
09-02 14:10:13.902  3951  4191 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"rm_id_list":[],"backup_data":"{\"22\":1756793937,\"11\":1756793637,\"12\":1756793637,\"35\":1757386223,\"13\":1756793637,\"9\":1756793637,\"check\":85755,\"10\":1756793637,\"98\":1756793467}","server_time":1756793414,"invalid_module_list":[],"list":[],"rm_close_list":[]}
09-02 14:10:14.191  3951  3951 I XposedBridge: PDDJsonDataHook: 🎯 JSONObject解析商品数据
09-02 14:10:14.191  3951  3951 I XposedBridge: PDDJsonDataHook: 📄 JSON数据: {"single_price":"9.12","line_price":"10.12","sales_tip":"总售114.3万件","prefix_text":"快要抢光","tag_desc":"大促直降7.55元","type":8,"prefix_icon":"https://img.pddpic.com/a/coupon/50152815-70ca-45b0-b0ff-2ea77f32f4fe.png","pindan_price":"0.57","desc_labels":"总售114.3万件"}