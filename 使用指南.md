# 拼多多Hook模块使用指南

## 📋 概述

基于您提供的运行结果分析，我已经对拼多多Xposed Hook模块进行了全面优化，主要改进包括：

### 🎯 核心改进
1. **专项商品详情Hook** - 精准捕获商品数据
2. **JSON数据解析Hook** - 拦截所有JSON解析过程
3. **配置化管理** - 支持动态配置Hook行为
4. **智能数据过滤** - 减少无关数据干扰
5. **增强日志系统** - 结构化输出商品信息

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保以下环境已准备就绪
- Android设备已root
- 已安装Xposed框架 (LSPosed/EdXposed)
- 已安装拼多多应用
- ADB工具可用
```

### 2. 构建和安装
```bash
# 使用提供的构建脚本
build_and_test.bat

# 或手动构建
cd pdd_nixinag
./gradlew assembleDebug
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

### 3. 启用模块
1. 打开Xposed管理器
2. 在"模块"页面启用"PDD Hook Module"
3. 重启拼多多应用

### 4. 验证效果
```bash
# 使用验证脚本
python verify_hook.py

# 或手动查看日志
adb logcat | grep "PDDGoodsDetailHook\|PDDJsonDataHook"
```

## 📊 Hook模块详解

### 1. PDDGoodsDetailHook (优先级1)
**功能**: 专项商品详情数据捕获

**Hook目标**:
- `GoodsResponse` 对象创建
- `ProductDetailFragment` 数据加载
- `GoodsViewModel` 数据管理
- `GoodsDetailSkuDataProvider` 数据提供
- 商品详情API网络请求

**预期输出**:
```
🎯========== GoodsResponse构造 ==========
📦 对象类型: com.xunmeng.pinduoduo.goods.entity.GoodsResponse
⏰ 时间: Tue Sep 02 13:40:15 GMT+08:00 2025
----------------------------------------
  📋 goodsId: String(768634227682)
  📋 goodsName: String(商品名称)
  📋 price: Long(1999)
  📋 thumbUrl: String(https://...)
==========================================
```

### 2. PDDJsonDataHook (优先级2)
**功能**: JSON数据解析拦截

**Hook目标**:
- Gson JSON解析
- JSONObject解析
- FastJson解析
- 拼多多自定义JSON解析类

**预期输出**:
```
🎯 Gson解析商品JSON - 类型: GoodsResponse
📄 JSON数据: {"goods_id":768634227682,"goods_name":"...","price":1999,...}
```

### 3. 配置管理系统
**配置文件**: `app/src/main/assets/hook_config.json`

**主要配置项**:
```json
{
  "modules": {
    "PDDGoodsDetailHook": {
      "enabled": true,
      "priority": 1,
      "hooks": {
        "goods_response_creation": {"enabled": true},
        "product_detail_fragment": {"enabled": true}
      }
    }
  }
}
```

## 🔧 配置说明

### 全局设置
- `enable_logging`: 是否启用日志记录
- `max_log_length`: 最大日志长度
- `hook_delay_ms`: Hook延迟时间

### 模块设置
- `enabled`: 模块是否启用
- `priority`: 模块优先级
- `hooks`: 具体Hook配置

### 输出设置
- `max_field_count`: 最大字段显示数量
- `value_max_length`: 值的最大长度
- `include_timestamp`: 是否包含时间戳

## 📈 使用场景

### 场景1: 商品详情数据获取
1. 打开拼多多应用
2. 浏览商品列表
3. 点击进入商品详情页面
4. 查看日志输出的商品数据

### 场景2: API响应分析
1. 启用网络请求Hook
2. 浏览商品页面
3. 查看捕获的API响应JSON

### 场景3: 数据结构分析
1. 启用详细日志模式
2. 分析商品实体类结构
3. 了解数据字段含义

## 🛠️ 故障排除

### 问题1: 没有日志输出
**可能原因**:
- Xposed模块未启用
- 拼多多应用未重启
- 配置文件中模块被禁用

**解决方案**:
```bash
# 检查模块状态
adb shell "pm list packages | grep pdd_nixinag"

# 检查Xposed日志
adb logcat | grep "Xposed"

# 重启应用
adb shell "am force-stop com.xunmeng.pinduoduo"
```

### 问题2: 应用闪退
**可能原因**:
- Hook过于激进
- 版本不兼容
- 内存不足

**解决方案**:
1. 在配置文件中禁用部分Hook
2. 降低Hook频率
3. 检查目标类是否存在

### 问题3: 数据不完整
**可能原因**:
- Hook时机不对
- 数据加载延迟
- 字段过滤过严

**解决方案**:
1. 增加Hook延迟时间
2. 调整字段过滤规则
3. 启用更多Hook点

## 📊 性能优化

### 1. Hook数量控制
- 限制每个类的Hook数量
- 使用配置文件动态控制
- 避免Hook过于频繁的方法

### 2. 日志优化
- 限制日志长度
- 过滤无关信息
- 使用异步日志记录

### 3. 内存管理
- 及时释放大对象
- 避免内存泄漏
- 监控内存使用

## 🔄 版本更新

### v2.0.0 (当前版本)
- ✅ 基于运行结果的精准优化
- ✅ 配置化管理系统
- ✅ 增强的JSON数据捕获
- ✅ 智能数据过滤
- ✅ 结构化日志输出

### 后续计划
- 🔄 支持更多拼多多版本
- 🔄 添加数据导出功能
- 🔄 实时监控界面
- 🔄 自动化测试框架

## 📞 技术支持

### 日志收集
```bash
# 收集完整日志
adb logcat > pdd_hook_full.log

# 收集Hook相关日志
adb logcat | grep "PDD.*Hook" > pdd_hook_filtered.log
```

### 配置验证
```bash
# 验证配置文件
python -m json.tool pdd_nixinag/app/src/main/assets/hook_config.json
```

### 性能监控
```bash
# 监控应用性能
adb shell "top | grep pinduoduo"
```

## ⚠️ 注意事项

1. **法律合规**: 请遵守相关法律法规和平台服务条款
2. **版本兼容**: 基于当前拼多多版本优化，其他版本可能需要调整
3. **性能影响**: Hook可能对应用性能有轻微影响
4. **稳定性**: 如遇闪退，请适当减少Hook范围
5. **隐私保护**: 请妥善处理获取的数据，保护用户隐私

## 📚 相关文档

- [拼多多Hook模块优化总结.md](拼多多Hook模块优化总结.md) - 详细的优化分析
- [hook_config.json](pdd_nixinag/app/src/main/assets/hook_config.json) - 配置文件说明
- [verify_hook.py](verify_hook.py) - 验证脚本使用说明
- [build_and_test.bat](build_and_test.bat) - 构建脚本说明

---

**最后更新**: 2025-09-02  
**版本**: v2.0.0  
**兼容性**: 拼多多 6.0+ / Android 5.0+ / Xposed框架
