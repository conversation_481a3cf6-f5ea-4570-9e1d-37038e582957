package com.example.pdd_nixinag;

import android.util.Log;
import de.robv.android.xposed.XposedBridge;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 日志工具类
 * 统一管理Xposed和Android日志输出
 */
public class LogUtils {
    
    private static final String XPOSED_TAG = "PDDHook";
    private static final String ANDROID_TAG = "PDDHook";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault());
    
    /**
     * 输出信息日志
     */
    public static void i(String message) {
        String timestamp = getTimestamp();
        String fullMessage = "[" + timestamp + "] " + message;
        
        XposedBridge.log(XPOSED_TAG + ": " + fullMessage);
        Log.i(ANDROID_TAG, fullMessage);
    }
    
    /**
     * 输出调试日志
     */
    public static void d(String message) {
        String timestamp = getTimestamp();
        String fullMessage = "[" + timestamp + "] " + message;
        
        XposedBridge.log(XPOSED_TAG + ": " + fullMessage);
        Log.d(ANDROID_TAG, fullMessage);
    }
    
    /**
     * 输出警告日志
     */
    public static void w(String message) {
        String timestamp = getTimestamp();
        String fullMessage = "[" + timestamp + "] " + message;
        
        XposedBridge.log(XPOSED_TAG + ": " + fullMessage);
        Log.w(ANDROID_TAG, fullMessage);
    }
    
    /**
     * 输出错误日志
     */
    public static void e(String message) {
        String timestamp = getTimestamp();
        String fullMessage = "[" + timestamp + "] " + message;
        
        XposedBridge.log(XPOSED_TAG + ": " + fullMessage);
        Log.e(ANDROID_TAG, fullMessage);
    }
    
    /**
     * 输出错误日志（带异常）
     */
    public static void e(String message, Throwable throwable) {
        String timestamp = getTimestamp();
        String fullMessage = "[" + timestamp + "] " + message + " - " + throwable.getMessage();
        
        XposedBridge.log(XPOSED_TAG + ": " + fullMessage);
        Log.e(ANDROID_TAG, fullMessage, throwable);
    }
    
    /**
     * 输出商品信息日志
     */
    public static void logGoods(String title, Object goodsData) {
        if (goodsData == null) {
            i("商品数据为空: " + title);
            return;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("\n").append("=".repeat(50)).append("\n");
        sb.append("🛍️ 商品数据: ").append(title).append("\n");
        sb.append("📦 对象类型: ").append(goodsData.getClass().getSimpleName()).append("\n");
        sb.append("⏰ 捕获时间: ").append(getTimestamp()).append("\n");
        sb.append("-".repeat(30)).append("\n");
        
        // 使用反射获取字段
        try {
            java.lang.reflect.Field[] fields = goodsData.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(goodsData);
                    if (value != null) {
                        String fieldName = field.getName();
                        String valueStr = truncateString(value.toString(), 300);
                        sb.append("  📋 ").append(fieldName).append(": ").append(valueStr).append("\n");
                    }
                } catch (Exception e) {
                    // 忽略无法访问的字段
                }
            }
        } catch (Exception e) {
            sb.append("  ❌ 反射获取字段失败: ").append(e.getMessage()).append("\n");
        }
        
        sb.append("=".repeat(50)).append("\n");
        
        i(sb.toString());
    }
    
    /**
     * 输出分类信息日志
     */
    public static void logCategory(String title, Object categoryData) {
        if (categoryData == null) {
            i("分类数据为空: " + title);
            return;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("\n").append("=".repeat(50)).append("\n");
        sb.append("📂 分类数据: ").append(title).append("\n");
        sb.append("📁 对象类型: ").append(categoryData.getClass().getSimpleName()).append("\n");
        sb.append("⏰ 捕获时间: ").append(getTimestamp()).append("\n");
        sb.append("-".repeat(30)).append("\n");
        
        // 使用反射获取字段
        try {
            java.lang.reflect.Field[] fields = categoryData.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(categoryData);
                    if (value != null) {
                        String fieldName = field.getName();
                        String valueStr = truncateString(value.toString(), 300);
                        sb.append("  🗂️ ").append(fieldName).append(": ").append(valueStr).append("\n");
                    }
                } catch (Exception e) {
                    // 忽略无法访问的字段
                }
            }
        } catch (Exception e) {
            sb.append("  ❌ 反射获取字段失败: ").append(e.getMessage()).append("\n");
        }
        
        sb.append("=".repeat(50)).append("\n");
        
        i(sb.toString());
    }
    
    /**
     * 输出网络请求日志
     */
    public static void logNetwork(String method, String url, String data) {
        StringBuilder sb = new StringBuilder();
        sb.append("\n").append("=".repeat(40)).append("\n");
        sb.append("🌐 网络请求: ").append(method).append("\n");
        sb.append("🔗 URL: ").append(url).append("\n");
        sb.append("⏰ 时间: ").append(getTimestamp()).append("\n");
        if (data != null && !data.isEmpty()) {
            sb.append("📄 数据: ").append(truncateString(data, 500)).append("\n");
        }
        sb.append("=".repeat(40)).append("\n");
        
        i(sb.toString());
    }
    
    /**
     * 输出方法调用日志
     */
    public static void logMethodCall(String className, String methodName, Object[] args, Object result) {
        StringBuilder sb = new StringBuilder();
        sb.append("📞 方法调用: ").append(className).append(".").append(methodName);
        
        if (args != null && args.length > 0) {
            sb.append("(");
            for (int i = 0; i < args.length; i++) {
                if (i > 0) sb.append(", ");
                sb.append(args[i] != null ? truncateString(args[i].toString(), 50) : "null");
            }
            sb.append(")");
        } else {
            sb.append("()");
        }
        
        if (result != null) {
            sb.append(" -> ").append(truncateString(result.toString(), 100));
        }
        
        d(sb.toString());
    }
    
    /**
     * 输出Hook状态日志
     */
    public static void logHookStatus(String className, String methodName, boolean success) {
        String status = success ? "✅ 成功" : "❌ 失败";
        i("Hook状态: " + status + " - " + className + "." + methodName);
    }
    
    /**
     * 输出Hook成功统计
     */
    public static void logHookSummary(String className, int successCount, int totalCount) {
        double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0;
        i(String.format("📊 Hook统计 - %s: %d/%d (%.1f%%)", className, successCount, totalCount, successRate));
    }
    
    /**
     * 获取当前时间戳
     */
    private static String getTimestamp() {
        return DATE_FORMAT.format(new Date());
    }
    
    /**
     * 截断字符串
     */
    private static String truncateString(String str, int maxLength) {
        if (str == null) return "null";
        if (str.length() <= maxLength) return str;
        return str.substring(0, maxLength) + "...";
    }
}