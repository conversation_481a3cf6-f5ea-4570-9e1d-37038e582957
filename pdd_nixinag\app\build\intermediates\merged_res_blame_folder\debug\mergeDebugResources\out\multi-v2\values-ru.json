{"logs": [{"outputFile": "com.example.pdd_nixinag.app-mergeDebugResources-29:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\adbd1bf8c0dde738d4ae689d2cff7ec7\\transformed\\core-1.13.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3577,3675,3777,3878,3979,4084,4187,10078", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3670,3772,3873,3974,4079,4182,4299,10174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd66567268b16531d74704a824cda5df\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3388,3486,4304,4401,4538,4630,4705,4771,4870,4947,5010,5128,5189,5254,5311,5381,5442,5496,5612,5669,5731,5785,5859,5987,6075,6162,6265,6357,6443,6580,6664,6749,6883,6974,7050,7104,7155,7221,7293,7371,7442,7524,7604,7680,7757,7834,7941,8030,8103,8193,8288,8362,8443,8536,8591,8672,8738,8824,8909,8971,9035,9098,9170,9268,9367,9462,9554,9612,9667,9829,9923,9999", "endLines": "7,35,36,37,38,39,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,116,117", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "426,3221,3299,3383,3481,3572,4396,4533,4625,4700,4766,4865,4942,5005,5123,5184,5249,5306,5376,5437,5491,5607,5664,5726,5780,5854,5982,6070,6157,6260,6352,6438,6575,6659,6744,6878,6969,7045,7099,7150,7216,7288,7366,7437,7519,7599,7675,7752,7829,7936,8025,8098,8188,8283,8357,8438,8531,8586,8667,8733,8819,8904,8966,9030,9093,9165,9263,9362,9457,9549,9607,9662,9742,9918,9994,10073"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d98712013b95214d35136c273936228a\\transformed\\appcompat-1.7.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,9747", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,9824"}}]}]}