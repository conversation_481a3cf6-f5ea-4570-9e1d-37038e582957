package com.example.pdd_nixinag;

import android.util.Log;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage.LoadPackageParam;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 专门Hook JSON数据解析，捕获商品详情的JSON响应
 * 基于运行结果中发现的JSON解析模式
 */
public class PDDJsonDataHook implements IXposedHookLoadPackage {

    private static final String TAG = "PDDJsonDataHook";
    private static final String PDD_PACKAGE = "com.xunmeng.pinduoduo";

    private static PDDHttpServer httpServer;
    private static final Gson gson = new Gson();
    private static final AtomicInteger dataCounter = new AtomicInteger(0);

    // 商品数据聚合器
    private static final GoodsDataAggregator goodsAggregator = new GoodsDataAggregator();
    
    @Override
    public void handleLoadPackage(LoadPackageParam lpparam) throws Throwable {
        if (!PDD_PACKAGE.equals(lpparam.packageName)) {
            return;
        }

        XposedBridge.log(TAG + ": 开始Hook JSON数据解析");
        XposedBridge.log(TAG + ": " + NetworkConfig.getConfigInfo());

        // 启动HTTP服务器
        startHttpServer();

        // 初始化商品数据聚合器
        XposedBridge.log(TAG + ": 初始化商品数据聚合器");

        // Hook各种JSON解析方法
        hookGsonParsing(lpparam);
        hookJSONObjectParsing(lpparam);
        hookFastJsonParsing(lpparam);
        hookCustomJsonParsing(lpparam);

        XposedBridge.log(TAG + ": JSON Hook注入完成");
    }

    /**
     * 启动HTTP服务器
     */
    private void startHttpServer() {
        try {
            if (httpServer == null || !PDDHttpServer.isServerRunning()) {
                httpServer = PDDHttpServer.getInstance();
                if (httpServer != null) {
                    XposedBridge.log(TAG + ": ✅ HTTP服务器启动成功，端口: " + PDDHttpServer.getServerPort());
                } else {
                    XposedBridge.log(TAG + ": ❌ HTTP服务器启动失败");
                }
            }
        } catch (Exception e) {
            XposedBridge.log(TAG + ": HTTP服务器启动异常: " + e.getMessage());
        }
    }
    
    /**
     * Hook Gson解析 - 运行结果显示使用了Gson
     */
    private void hookGsonParsing(LoadPackageParam lpparam) {
        try {
            Class<?> gsonClass = XposedHelpers.findClass("com.google.gson.Gson", lpparam.classLoader);
            
            // Hook fromJson(String, Class)
            XposedHelpers.findAndHookMethod(gsonClass, "fromJson", String.class, Class.class, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    String json = (String) param.args[0];
                    Class<?> clazz = (Class<?>) param.args[1];
                    
                    if (isGoodsRelatedJson(json, clazz)) {
                        XposedBridge.log(TAG + ": 🎯 捕获原始JSON数据 - Gson解析");
                        XposedBridge.log(TAG + ": 📄 原始JSON: " + json);

                        // 添加到商品数据聚合器
                        goodsAggregator.addGoodsData(json, "Gson", clazz.getSimpleName());
                    }
                }
                
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    Object result = param.getResult();
                    if (result != null && isGoodsRelatedObject(result)) {
                        XposedBridge.log(TAG + ": ✅ Gson解析结果: " + result.getClass().getSimpleName());
                        logGoodsObject(result, "Gson解析结果");
                    }
                }
            });
            
            // Hook fromJson(String, Type)
            XposedHelpers.findAndHookMethod(gsonClass, "fromJson", String.class, 
                java.lang.reflect.Type.class, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    String json = (String) param.args[0];
                    java.lang.reflect.Type type = (java.lang.reflect.Type) param.args[1];
                    
                    if (isGoodsRelatedJson(json, type)) {
                        XposedBridge.log(TAG + ": 🎯 捕获原始JSON数据 - Gson Type解析");
                        XposedBridge.log(TAG + ": 📄 原始JSON: " + json);

                        // 添加到商品数据聚合器
                        goodsAggregator.addGoodsData(json, "Gson", type.getTypeName());
                    }
                }
            });
            
            XposedBridge.log(TAG + ": 成功Hook Gson解析");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook Gson失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook JSONObject解析
     */
    private void hookJSONObjectParsing(LoadPackageParam lpparam) {
        try {
            Class<?> jsonObjectClass = XposedHelpers.findClass("org.json.JSONObject", lpparam.classLoader);
            
            // Hook JSONObject构造方法
            XposedHelpers.findAndHookConstructor(jsonObjectClass, String.class, new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    String json = (String) param.args[0];
                    if (isGoodsRelatedJson(json, null)) {
                        XposedBridge.log(TAG + ": 🎯 捕获原始JSON数据 - JSONObject解析");
                        XposedBridge.log(TAG + ": 📄 原始JSON: " + json);

                        // 添加到商品数据聚合器
                        goodsAggregator.addGoodsData(json, "JSONObject", "JSONObject");
                    }
                }
            });
            
            XposedBridge.log(TAG + ": 成功Hook JSONObject");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook JSONObject失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook FastJson解析
     */
    private void hookFastJsonParsing(LoadPackageParam lpparam) {
        try {
            Class<?> jsonClass = XposedHelpers.findClass("com.alibaba.fastjson.JSON", lpparam.classLoader);
            
            // Hook parseObject方法
            XposedHelpers.findAndHookMethod(jsonClass, "parseObject", String.class, Class.class, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    String json = (String) param.args[0];
                    Class<?> clazz = (Class<?>) param.args[1];
                    
                    if (isGoodsRelatedJson(json, clazz)) {
                        XposedBridge.log(TAG + ": 🎯 捕获原始JSON数据 - FastJson解析");
                        XposedBridge.log(TAG + ": 📄 原始JSON: " + json);

                        // 添加到商品数据聚合器
                        goodsAggregator.addGoodsData(json, "FastJson", clazz.getSimpleName());
                    }
                }
            });
            
            XposedBridge.log(TAG + ": 成功Hook FastJson");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook FastJson失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook拼多多自定义JSON解析类
     */
    private void hookCustomJsonParsing(LoadPackageParam lpparam) {
        // 基于运行结果，尝试Hook可能的自定义JSON解析类
        String[] possibleJsonClasses = {
            "com.xunmeng.pinduoduo.basekit.util.JSONFormatUtils",
            "com.xunmeng.pinduoduo.util.JsonUtil",
            "com.xunmeng.pinduoduo.common.JsonParser",
            "com.xunmeng.pinduoduo.network.JsonResponseParser"
        };
        
        for (String className : possibleJsonClasses) {
            try {
                Class<?> jsonUtilClass = XposedHelpers.findClass(className, lpparam.classLoader);
                
                // Hook所有可能的解析方法
                java.lang.reflect.Method[] methods = jsonUtilClass.getDeclaredMethods();
                for (java.lang.reflect.Method method : methods) {
                    String methodName = method.getName();
                    if (methodName.contains("parse") || methodName.contains("from") || 
                        methodName.contains("decode") || methodName.contains("json")) {
                        
                        try {
                            XposedHelpers.findAndHookMethod(jsonUtilClass, methodName, 
                                method.getParameterTypes(), new XC_MethodHook() {
                                    @Override
                                    protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                                        // 检查参数中是否有JSON字符串
                                        for (Object arg : param.args) {
                                            if (arg instanceof String) {
                                                String json = (String) arg;
                                                if (isGoodsRelatedJson(json, null)) {
                                                    XposedBridge.log(TAG + ": 🎯 自定义JSON解析 " +
                                                        className + "." + methodName);
                                                    XposedBridge.log(TAG + ": 📄 完整JSON数据: " + json);

                                                    // 添加到商品数据聚合器
                                                    goodsAggregator.addGoodsData(json, "CustomJson", className + "." + methodName);
                                                }
                                            }
                                        }
                                    }
                                    
                                    @Override
                                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                                        Object result = param.getResult();
                                        if (result != null && isGoodsRelatedObject(result)) {
                                            logGoodsObject(result, "自定义JSON解析结果");
                                        }
                                    }
                                }
                            );
                        } catch (Exception e) {
                            // 某些方法可能Hook失败，继续处理其他方法
                        }
                    }
                }
                
                XposedBridge.log(TAG + ": 成功Hook自定义JSON类: " + className);
            } catch (Exception e) {
                // 类不存在，继续尝试下一个
            }
        }
    }
    
    /**
     * 判断JSON是否与商品相关
     */
    private boolean isGoodsRelatedJson(String json, Object typeInfo) {
        if (json == null || json.length() < 50) return false;

        String lowerJson = json.toLowerCase();

        // 排除明显不相关的数据类型
        String[] excludeKeywords = {
            "md5_list", "component_id", "version", "manifest", "static.pddpic.com",
            "config", "strategy", "popup", "push", "badge", "notice", "score",
            "traffic", "monitor", "glide", "emoji", "gray", "visit", "usage",
            "statistics", "preload", "tab_list", "circle_info", "body_data",
            "page_stack", "page_type", "page_hash", "activity_name", "create_time",
            "error_code", "feed_id", "event_id", "show_id", "link_url", "video_url",
            "width", "height", "float_window", "replay_window"
        };

        // 如果包含排除关键词，直接返回false
        for (String exclude : excludeKeywords) {
            if (lowerJson.contains(exclude)) {
                return false;
            }
        }

        // 检查类型信息，排除不相关的类型
        if (typeInfo != null) {
            String typeName = typeInfo.toString().toLowerCase();
            String[] excludeTypes = {
                "md5checker", "fullvalue", "config", "strategy", "popup", "push",
                "badge", "notice", "score", "traffic", "monitor", "glide", "emoji",
                "gray", "visit", "usage", "statistics", "preload", "hashmap", "map",
                "pagestack", "httperror", "replaywindowinfo", "windowinfo", "error"
            };

            for (String excludeType : excludeTypes) {
                if (typeName.contains(excludeType)) {
                    return false;
                }
            }
        }

        // 检查商品相关的关键字
        String[] goodsKeywords = {
            "goods_id", "goodsid", "product_id", "productid", "item_id", "itemid",
            "goods_name", "goodsname", "product_name", "productname", "item_name",
            "goods_price", "product_price", "item_price", "single_price", "market_price",
            "goods_detail", "product_detail", "item_detail", "goods_info",
            "thumb_url", "image_url", "_oak_gallery", "gallery_token",
            "mall_id", "shop_id", "brand", "sales", "stock", "inventory",
            "sku_id", "spec", "attribute", "category_id", "cat_id"
        };

        int matchCount = 0;
        for (String keyword : goodsKeywords) {
            if (lowerJson.contains(keyword)) {
                matchCount++;
            }
        }

        // 需要匹配到至少2个关键字才认为是商品相关
        if (matchCount >= 2) {
            // 必须包含商品ID才认为是真正的商品数据
            boolean hasGoodsId = lowerJson.contains("goods_id") || lowerJson.contains("product_id") ||
                                lowerJson.contains("item_id") || lowerJson.contains("goodsid");

            if (hasGoodsId) {
                // 进一步验证：包含商品的任何相关信息即可
                boolean hasCoreInfo = lowerJson.contains("thumb_url") || lowerJson.contains("_oak_gallery") ||
                                    lowerJson.contains("goods_name") || lowerJson.contains("price") ||
                                    lowerJson.contains("mall_id") || lowerJson.contains("category") ||
                                    lowerJson.contains("url") || lowerJson.contains("page_from") ||
                                    lowerJson.contains("activity") || lowerJson.contains("route");

                // 如果有商品ID，即使没有其他信息也可能是有用的商品数据
                return hasCoreInfo || json.length() > 100; // 较长的JSON可能包含更多信息
            }
        }

        // 检查类型信息中的商品相关类型
        if (typeInfo != null) {
            String typeName = typeInfo.toString().toLowerCase();
            return (typeName.contains("goods") || typeName.contains("product") ||
                   typeName.contains("item")) &&
                   (typeName.contains("detail") || typeName.contains("info") ||
                    typeName.contains("render") || typeName.contains("response"));
        }

        return false;
    }
    
    /**
     * 判断对象是否与商品相关
     */
    private boolean isGoodsRelatedObject(Object obj) {
        if (obj == null) return false;
        String className = obj.getClass().getName().toLowerCase();
        return className.contains("goods") || className.contains("product") || 
               className.contains("item") || className.contains("sku") ||
               className.contains("entity") || className.contains("response") ||
               className.contains("mall") || className.contains("detail");
    }
    

    /**
     * 记录商品对象
     */
    private void logGoodsObject(Object obj, String source) {
        if (obj == null) return;
        
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("\n🎯========== ").append(source).append(" ==========\n");
            sb.append("📦 对象类型: ").append(obj.getClass().getName()).append("\n");
            
            // 使用反射获取重要字段
            java.lang.reflect.Field[] fields = obj.getClass().getDeclaredFields();
            int loggedCount = 0;
            
            for (java.lang.reflect.Field field : fields) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    
                    if (value != null && shouldLogField(field.getName(), value)) {
                        String valueDesc = getValueDescription(value);
                        sb.append("  📋 ").append(field.getName()).append(": ").append(valueDesc).append("\n");
                        loggedCount++;
                        
                        if (loggedCount >= 15) {
                            sb.append("  ... (更多字段已省略)\n");
                            break;
                        }
                    }
                } catch (Exception e) {
                    // 忽略无法访问的字段
                }
            }
            
            sb.append("==========================================\n");
            
            String logMessage = sb.toString();
            XposedBridge.log(TAG + ": " + logMessage);
            Log.d(TAG, logMessage);
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 记录商品对象失败: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否应该记录字段
     */
    private boolean shouldLogField(String fieldName, Object value) {
        if (value == null) return false;
        
        String lowerFieldName = fieldName.toLowerCase();
        String[] keywords = {
            "id", "name", "title", "price", "url", "image", "desc", "goods", "product", 
            "sku", "category", "mall", "data", "status", "count", "thumb", "pic", 
            "brand", "spec", "attr", "tag", "sales", "stock"
        };
        
        for (String keyword : keywords) {
            if (lowerFieldName.contains(keyword)) {
                return true;
            }
        }
        
        // 基本数据类型的非默认值
        if (value instanceof String && !((String) value).isEmpty()) return true;
        if (value instanceof Number && !value.equals(0) && !value.equals(0L)) return true;
        if (value instanceof Boolean) return true;
        
        return false;
    }
    
    /**
     * 获取值描述
     */
    private String getValueDescription(Object value) {
        if (value == null) return "null";

        String valueStr = value.toString();
        String className = value.getClass().getSimpleName();

        if (valueStr.length() > 100) {
            valueStr = valueStr.substring(0, 100) + "...";
        }

        return className + "(" + valueStr + ")";
    }

    /**
     * 发送聚合数据到Python监控脚本（静态方法供聚合器调用）
     */
    public static void sendAggregatedData(String aggregatedJsonData) {
        try {
            // 只发送到Python监控脚本，避免重复
            sendHttpRequestStatic(NetworkConfig.getPythonMonitorUrl(), aggregatedJsonData);

        } catch (Exception e) {
            XposedBridge.log(TAG + ": 发送聚合数据失败: " + e.getMessage());
        }
    }

    /**
     * 发送数据到Python监控脚本（保留原方法用于兼容）
     */
    private void sendDataToServer(String jsonData, String parseType, String dataType) {
        try {
            // 创建数据包
            JsonObject dataPacket = new JsonObject();
            dataPacket.addProperty("timestamp", System.currentTimeMillis());
            dataPacket.addProperty("counter", dataCounter.incrementAndGet());
            dataPacket.addProperty("parseType", parseType);
            dataPacket.addProperty("dataType", dataType);
            dataPacket.addProperty("packageName", PDD_PACKAGE);
            dataPacket.addProperty("jsonData", jsonData);

            String packetJson = gson.toJson(dataPacket);

            // 发送到Python监控脚本 - 使用配置的电脑IP地址
            sendHttpRequest(NetworkConfig.getPythonMonitorUrl(), packetJson);

            // 同时发送到本地HTTP服务器（如果存在）
            if (httpServer != null && PDDHttpServer.isServerRunning()) {
                sendHttpRequest(NetworkConfig.getXposedServerUrl(PDDHttpServer.getServerPort()), packetJson);
            }

        } catch (Exception e) {
            XposedBridge.log(TAG + ": 发送数据到服务器失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTTP请求
     */
    private void sendHttpRequest(String urlString, String jsonData) {
        new Thread(() -> {
            try {
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();

                // 设置请求方法和属性
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
                connection.setRequestProperty("User-Agent", "PDDJsonDataHook/1.0");
                connection.setDoOutput(true);
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(10000);

                // 发送数据
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = jsonData.getBytes("utf-8");
                    os.write(input, 0, input.length);
                }

                // 获取响应
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    XposedBridge.log(TAG + ": ✅ 数据发送成功");
                } else {
                    XposedBridge.log(TAG + ": ❌ 数据发送失败，响应码: " + responseCode);
                }

                connection.disconnect();

            } catch (Exception e) {
                XposedBridge.log(TAG + ": 发送HTTP请求异常: " + e.getMessage());
            }
        }).start();
    }

    /**
     * 静态HTTP请求方法（供聚合器调用）
     */
    public static void sendHttpRequestStatic(String urlString, String jsonData) {
        new Thread(() -> {
            try {
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();

                // 设置请求方法和属性
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
                connection.setRequestProperty("User-Agent", "PDDJsonDataHook/1.0");
                connection.setDoOutput(true);
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(10000);

                // 发送数据
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = jsonData.getBytes("utf-8");
                    os.write(input, 0, input.length);
                }

                // 获取响应
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    XposedBridge.log(TAG + ": ✅ 聚合数据发送成功");
                } else {
                    XposedBridge.log(TAG + ": ❌ 聚合数据发送失败，响应码: " + responseCode);
                }

                connection.disconnect();

            } catch (Exception e) {
                XposedBridge.log(TAG + ": 发送聚合数据HTTP请求异常: " + e.getMessage());
            }
        }).start();
    }
}
