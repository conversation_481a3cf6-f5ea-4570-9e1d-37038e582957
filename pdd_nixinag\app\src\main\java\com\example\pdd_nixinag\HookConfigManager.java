package com.example.pdd_nixinag;

import android.content.Context;
import de.robv.android.xposed.XposedBridge;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * Hook配置管理器
 * 读取和管理hook_config.json配置文件
 */
public class HookConfigManager {
    
    private static final String TAG = "HookConfigManager";
    private static final String CONFIG_FILE = "hook_config.json";
    
    private static HookConfigManager instance;
    private JSONObject config;
    private boolean configLoaded = false;
    
    private HookConfigManager() {
        loadConfig();
    }
    
    public static synchronized HookConfigManager getInstance() {
        if (instance == null) {
            instance = new HookConfigManager();
        }
        return instance;
    }
    
    /**
     * 从assets加载配置文件
     */
    private void loadConfig() {
        try {
            // 尝试从多个可能的位置加载配置
            String configContent = loadConfigFromAssets();
            if (configContent == null) {
                configContent = getDefaultConfig();
            }
            
            config = new JSONObject(configContent);
            configLoaded = true;
            XposedBridge.log(TAG + ": 配置文件加载成功");
            
        } catch (JSONException e) {
            XposedBridge.log(TAG + ": 配置文件解析失败: " + e.getMessage());
            config = getDefaultConfigObject();
            configLoaded = false;
        }
    }
    
    /**
     * 从assets目录加载配置文件
     */
    private String loadConfigFromAssets() {
        try {
            // 由于Xposed环境的限制，我们需要通过反射获取assets
            Class<?> contextClass = Class.forName("android.app.ActivityThread");
            Object activityThread = contextClass.getMethod("currentApplication").invoke(null);
            if (activityThread != null) {
                Context context = (Context) activityThread;
                InputStream is = context.getAssets().open(CONFIG_FILE);
                return readInputStream(is);
            }
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 从assets加载配置失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 读取输入流内容
     */
    private String readInputStream(InputStream is) throws IOException {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
        String line;
        while ((line = reader.readLine()) != null) {
            sb.append(line).append("\n");
        }
        reader.close();
        return sb.toString();
    }
    
    /**
     * 获取默认配置
     */
    private String getDefaultConfig() {
        return "{\n" +
                "  \"version\": \"2.0.0\",\n" +
                "  \"global_settings\": {\n" +
                "    \"enable_logging\": true,\n" +
                "    \"log_level\": \"INFO\",\n" +
                "    \"max_log_length\": 1000\n" +
                "  },\n" +
                "  \"modules\": {\n" +
                "    \"PDDGoodsDetailHook\": {\n" +
                "      \"enabled\": true,\n" +
                "      \"priority\": 1\n" +
                "    },\n" +
                "    \"PDDJsonDataHook\": {\n" +
                "      \"enabled\": true,\n" +
                "      \"priority\": 2\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }
    
    /**
     * 获取默认配置对象
     */
    private JSONObject getDefaultConfigObject() {
        try {
            return new JSONObject(getDefaultConfig());
        } catch (JSONException e) {
            return new JSONObject();
        }
    }
    
    // ========== 配置读取方法 ==========
    
    /**
     * 检查模块是否启用
     */
    public boolean isModuleEnabled(String moduleName) {
        try {
            return config.getJSONObject("modules")
                    .getJSONObject(moduleName)
                    .optBoolean("enabled", true);
        } catch (JSONException e) {
            return true; // 默认启用
        }
    }
    
    /**
     * 获取模块优先级
     */
    public int getModulePriority(String moduleName) {
        try {
            return config.getJSONObject("modules")
                    .getJSONObject(moduleName)
                    .optInt("priority", 999);
        } catch (JSONException e) {
            return 999;
        }
    }
    
    /**
     * 检查Hook是否启用
     */
    public boolean isHookEnabled(String moduleName, String hookName) {
        try {
            return config.getJSONObject("modules")
                    .getJSONObject(moduleName)
                    .getJSONObject("hooks")
                    .getJSONObject(hookName)
                    .optBoolean("enabled", true);
        } catch (JSONException e) {
            return true; // 默认启用
        }
    }
    
    /**
     * 获取Hook的目标类名
     */
    public String getHookClassName(String moduleName, String hookName) {
        try {
            return config.getJSONObject("modules")
                    .getJSONObject(moduleName)
                    .getJSONObject("hooks")
                    .getJSONObject(hookName)
                    .optString("class", "");
        } catch (JSONException e) {
            return "";
        }
    }
    
    /**
     * 获取Hook的目标方法列表
     */
    public List<String> getHookMethods(String moduleName, String hookName) {
        List<String> methods = new ArrayList<>();
        try {
            JSONArray methodsArray = config.getJSONObject("modules")
                    .getJSONObject(moduleName)
                    .getJSONObject("hooks")
                    .getJSONObject(hookName)
                    .optJSONArray("methods");
            
            if (methodsArray != null) {
                for (int i = 0; i < methodsArray.length(); i++) {
                    methods.add(methodsArray.getString(i));
                }
            }
        } catch (JSONException e) {
            // 返回空列表
        }
        return methods;
    }
    
    /**
     * 获取关键字段列表
     */
    public List<String> getCriticalFields(String moduleName) {
        List<String> fields = new ArrayList<>();
        try {
            JSONArray fieldsArray = config.getJSONObject("modules")
                    .getJSONObject(moduleName)
                    .getJSONObject("field_filters")
                    .optJSONArray("critical_fields");
            
            if (fieldsArray != null) {
                for (int i = 0; i < fieldsArray.length(); i++) {
                    fields.add(fieldsArray.getString(i));
                }
            }
        } catch (JSONException e) {
            // 返回默认字段
            fields.add("goodsId");
            fields.add("goodsName");
            fields.add("price");
        }
        return fields;
    }
    
    /**
     * 获取重要关键词列表
     */
    public List<String> getImportantKeywords(String moduleName) {
        List<String> keywords = new ArrayList<>();
        try {
            JSONArray keywordsArray = config.getJSONObject("modules")
                    .getJSONObject(moduleName)
                    .getJSONObject("field_filters")
                    .optJSONArray("important_keywords");
            
            if (keywordsArray != null) {
                for (int i = 0; i < keywordsArray.length(); i++) {
                    keywords.add(keywordsArray.getString(i));
                }
            }
        } catch (JSONException e) {
            // 返回默认关键词
            keywords.add("goods");
            keywords.add("product");
            keywords.add("price");
            keywords.add("name");
        }
        return keywords;
    }
    
    /**
     * 获取商品JSON关键词
     */
    public List<String> getGoodsJsonKeywords() {
        List<String> keywords = new ArrayList<>();
        try {
            JSONArray keywordsArray = config.getJSONObject("modules")
                    .getJSONObject("PDDJsonDataHook")
                    .getJSONObject("json_filters")
                    .optJSONArray("goods_keywords");
            
            if (keywordsArray != null) {
                for (int i = 0; i < keywordsArray.length(); i++) {
                    keywords.add(keywordsArray.getString(i));
                }
            }
        } catch (JSONException e) {
            // 返回默认关键词
            keywords.add("goods_id");
            keywords.add("goods_name");
            keywords.add("price");
        }
        return keywords;
    }
    
    /**
     * 获取全局设置
     */
    public boolean isLoggingEnabled() {
        try {
            return config.getJSONObject("global_settings")
                    .optBoolean("enable_logging", true);
        } catch (JSONException e) {
            return true;
        }
    }
    
    public int getMaxLogLength() {
        try {
            return config.getJSONObject("global_settings")
                    .optInt("max_log_length", 1000);
        } catch (JSONException e) {
            return 1000;
        }
    }
    
    public int getHookDelayMs() {
        try {
            return config.getJSONObject("global_settings")
                    .optInt("hook_delay_ms", 100);
        } catch (JSONException e) {
            return 100;
        }
    }
    
    /**
     * 获取JSON相关设置
     */
    public int getMaxJsonLength(String moduleName, String hookName) {
        try {
            return config.getJSONObject("modules")
                    .getJSONObject(moduleName)
                    .getJSONObject("hooks")
                    .getJSONObject(hookName)
                    .optInt("max_json_length", 600);
        } catch (JSONException e) {
            return 600;
        }
    }
    
    public int getMinKeywordMatches() {
        try {
            return config.getJSONObject("modules")
                    .getJSONObject("PDDJsonDataHook")
                    .getJSONObject("json_filters")
                    .optInt("min_keyword_matches", 2);
        } catch (JSONException e) {
            return 2;
        }
    }
    
    /**
     * 获取输出设置
     */
    public int getMaxFieldCount() {
        try {
            return config.getJSONObject("output_settings")
                    .optInt("max_field_count", 25);
        } catch (JSONException e) {
            return 25;
        }
    }
    
    public int getValueMaxLength() {
        try {
            return config.getJSONObject("output_settings")
                    .optInt("value_max_length", 150);
        } catch (JSONException e) {
            return 150;
        }
    }
    
    public boolean shouldIncludeTimestamp() {
        try {
            return config.getJSONObject("output_settings")
                    .optBoolean("include_timestamp", true);
        } catch (JSONException e) {
            return true;
        }
    }
    
    /**
     * 获取性能设置
     */
    public int getMaxHooksPerClass() {
        try {
            return config.getJSONObject("performance_settings")
                    .optInt("max_hooks_per_class", 10);
        } catch (JSONException e) {
            return 10;
        }
    }
    
    /**
     * 获取配置版本
     */
    public String getConfigVersion() {
        return config.optString("version", "1.0.0");
    }
    
    /**
     * 检查配置是否加载成功
     */
    public boolean isConfigLoaded() {
        return configLoaded;
    }
    
    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("配置版本: ").append(getConfigVersion()).append("\n");
        sb.append("配置状态: ").append(configLoaded ? "已加载" : "使用默认").append("\n");
        sb.append("日志启用: ").append(isLoggingEnabled()).append("\n");
        sb.append("最大日志长度: ").append(getMaxLogLength()).append("\n");
        
        try {
            JSONObject modules = config.getJSONObject("modules");
            sb.append("启用模块: ");
            for (String moduleName : new String[]{"PDDGoodsDetailHook", "PDDJsonDataHook", "PDDEnhancedHookModule", "PDDAdvancedHookModule"}) {
                if (isModuleEnabled(moduleName)) {
                    sb.append(moduleName).append(" ");
                }
            }
        } catch (JSONException e) {
            sb.append("模块信息获取失败");
        }
        
        return sb.toString();
    }
}
