@echo off
chcp 65001 >nul
echo ========================================
echo 拼多多Hook模块构建和测试脚本
echo ========================================
echo.

set PROJECT_DIR=%~dp0pdd_nixinag
set LOG_FILE=%~dp0hook_test_log.txt
set APK_FILE=%PROJECT_DIR%\app\build\outputs\apk\debug\app-debug.apk

echo 📁 项目目录: %PROJECT_DIR%
echo 📄 日志文件: %LOG_FILE%
echo 📦 APK文件: %APK_FILE%
echo.

:: 检查ADB连接
echo 🔍 检查ADB连接...
adb devices | findstr "device" >nul
if %errorlevel% neq 0 (
    echo ❌ 未检测到Android设备，请确保：
    echo    1. 手机已连接并开启USB调试
    echo    2. ADB驱动已正确安装
    echo    3. 手机已授权USB调试
    pause
    exit /b 1
)
echo ✅ ADB连接正常

:: 检查Gradle
echo.
echo 🔍 检查Gradle环境...
cd /d "%PROJECT_DIR%"
if not exist "gradlew.bat" (
    echo ❌ 未找到gradlew.bat，请确保在正确的项目目录
    pause
    exit /b 1
)
echo ✅ Gradle环境正常

:: 清理项目
echo.
echo 🧹 清理项目...
call gradlew.bat clean
if %errorlevel% neq 0 (
    echo ❌ 项目清理失败
    pause
    exit /b 1
)
echo ✅ 项目清理完成

:: 构建项目
echo.
echo 🔨 构建项目...
call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo ❌ 项目构建失败，请检查代码错误
    pause
    exit /b 1
)
echo ✅ 项目构建完成

:: 检查APK文件
if not exist "%APK_FILE%" (
    echo ❌ APK文件未生成: %APK_FILE%
    pause
    exit /b 1
)
echo ✅ APK文件生成成功

:: 安装APK
echo.
echo 📱 安装APK到设备...
adb install -r "%APK_FILE%"
if %errorlevel% neq 0 (
    echo ❌ APK安装失败
    pause
    exit /b 1
)
echo ✅ APK安装成功

:: 检查Xposed框架
echo.
echo 🔍 检查Xposed框架...
adb shell "pm list packages | grep de.robv.android.xposed.installer"
if %errorlevel% neq 0 (
    echo ⚠️  未检测到Xposed框架，请确保：
    echo    1. 手机已root
    echo    2. 已安装Xposed框架
    echo    3. 已重启手机激活框架
    echo.
    echo 是否继续测试？ (y/n)
    set /p continue=
    if /i not "%continue%"=="y" exit /b 1
)

:: 启动日志监控
echo.
echo 📊 启动日志监控...
echo 日志将保存到: %LOG_FILE%
echo 按 Ctrl+C 停止监控
echo.
echo ========================================
echo 请在手机上执行以下操作来测试Hook：
echo 1. 打开拼多多应用
echo 2. 浏览商品列表
echo 3. 点击进入商品详情页面
echo 4. 观察下方的日志输出
echo ========================================
echo.

:: 清空之前的日志
echo. > "%LOG_FILE%"

:: 开始监控日志
adb logcat -c
adb logcat | findstr "PDDGoodsDetailHook PDDJsonDataHook PDDEnhancedHook PDDAdvancedHook" | tee "%LOG_FILE%"

echo.
echo 📄 日志已保存到: %LOG_FILE%
echo 🎉 测试完成！
pause
