package com.example.pdd_nixinag;

import android.util.Log;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage.LoadPackageParam;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class PDDHookModule implements IXposedHookLoadPackage {

    private static final String TAG = "PDDHook";
    private static final String PDD_PACKAGE = "com.xunmeng.pinduoduo";

    @Override
    public void handleLoadPackage(LoadPackageParam lpparam) throws Throwable {
        if (!PDD_PACKAGE.equals(lpparam.packageName)) {
            return;
        }

        XposedBridge.log(TAG + ": 开始Hook拼多多应用 - 增强版本");

        // Hook 商品详情相关类 - 基于运行结果优化
        hookGoodsResponse(lpparam);
        hookGoodsEntity(lpparam);
        hookProductDetailFragment(lpparam);
        hookGoodsDetailSkuDataProvider(lpparam);
        hookNetworkRequests(lpparam);

        XposedBridge.log(TAG + ": Hook注入完成 - 增强版本");
    }
    
    /**
     * Hook GoodsResponse类 - 基于运行结果发现的关键类
     */
    private void hookGoodsResponse(LoadPackageParam lpparam) {
        try {
            Class<?> goodsResponseClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.goods.entity.GoodsResponse",
                lpparam.classLoader
            );

            // Hook构造方法
            XposedHelpers.findAndHookConstructor(goodsResponseClass, new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    XposedBridge.log(TAG + ": GoodsResponse对象已创建");
                    logGoodsInfo(param.thisObject, "新创建的商品响应");
                }
            });

            // Hook所有setter方法
            hookAllSetters(goodsResponseClass, "GoodsResponse");

            XposedBridge.log(TAG + ": 成功Hook GoodsResponse");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook GoodsResponse失败: " + e.getMessage());
        }
    }

    /**
     * Hook ProductDetailFragment - 基于运行结果的核心Fragment
     */
    private void hookProductDetailFragment(LoadPackageParam lpparam) {
        try {
            Class<?> fragmentClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.goods.ProductDetailFragment",
                lpparam.classLoader
            );

            // Hook onViewCreated方法 - 运行结果显示这里有商品数据
            XposedHelpers.findAndHookMethod(fragmentClass, "onViewCreated",
                android.view.View.class, android.os.Bundle.class, new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        XposedBridge.log(TAG + ": ProductDetailFragment视图已创建");

                        // 提取Fragment中的商品数据
                        extractFragmentGoodsData(param.thisObject);
                    }
                }
            );

            XposedBridge.log(TAG + ": 成功Hook ProductDetailFragment");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook ProductDetailFragment失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook GoodsEntity相关方法 - 优化版本
     */
    private void hookGoodsEntity(LoadPackageParam lpparam) {
        try {
            Class<?> goodsEntityClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.goods.entity.GoodsEntity",
                lpparam.classLoader
            );

            // Hook构造方法
            XposedHelpers.findAndHookConstructor(goodsEntityClass, new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    XposedBridge.log(TAG + ": GoodsEntity对象已创建");
                    logGoodsInfo(param.thisObject, "新创建的商品实体");
                }
            });

            // Hook重要的getter方法来获取商品数据
            hookImportantGetters(goodsEntityClass, "GoodsEntity");

            XposedBridge.log(TAG + ": 成功Hook GoodsEntity");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook GoodsEntity失败: " + e.getMessage());
        }
    }

    /**
     * Hook GoodsDetailSkuDataProvider - 基于运行结果的数据提供者
     */
    private void hookGoodsDetailSkuDataProvider(LoadPackageParam lpparam) {
        try {
            Class<?> dataProviderClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.goods.model.GoodsDetailSkuDataProvider",
                lpparam.classLoader
            );

            // Hook构造方法
            XposedHelpers.findAndHookConstructor(dataProviderClass,
                java.lang.ref.WeakReference.class, new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        XposedBridge.log(TAG + ": GoodsDetailSkuDataProvider已创建");

                        // 尝试获取Fragment引用中的商品数据
                        try {
                            Object weakRef = param.args[0];
                            if (weakRef != null) {
                                Object fragment = XposedHelpers.callMethod(weakRef, "get");
                                if (fragment != null) {
                                    extractFragmentGoodsData(fragment);
                                }
                            }
                        } catch (Exception e) {
                            XposedBridge.log(TAG + ": 从DataProvider获取Fragment数据失败: " + e.getMessage());
                        }
                    }
                }
            );

            XposedBridge.log(TAG + ": 成功Hook GoodsDetailSkuDataProvider");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook GoodsDetailSkuDataProvider失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook网络请求 - 捕获商品详情API响应
     */
    private void hookNetworkRequests(LoadPackageParam lpparam) {
        // Hook OkHttp Response
        try {
            Class<?> responseClass = XposedHelpers.findClass("okhttp3.Response", lpparam.classLoader);
            XposedHelpers.findAndHookMethod(responseClass, "body", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        Object response = param.thisObject;
                        Object request = XposedHelpers.callMethod(response, "request");
                        Object url = XposedHelpers.callMethod(request, "url");
                        String urlString = url.toString();

                        // 检查是否是商品详情相关的API
                        if (isGoodsDetailUrl(urlString)) {
                            XposedBridge.log(TAG + ": 捕获商品详情API: " + urlString);

                            // 尝试读取响应内容
                            Object responseBody = param.getResult();
                            if (responseBody != null) {
                                try {
                                    String content = XposedHelpers.callMethod(responseBody, "string").toString();
                                    if (content.length() > 100) { // 只记录有意义的响应
                                        XposedBridge.log(TAG + ": 商品详情API响应: " +
                                            (content.length() > 500 ? content.substring(0, 500) + "..." : content));
                                    }
                                } catch (Exception e) {
                                    XposedBridge.log(TAG + ": 读取响应内容失败: " + e.getMessage());
                                }
                            }
                        }
                    } catch (Exception e) {
                        // 忽略网络Hook中的异常
                    }
                }
            });

            XposedBridge.log(TAG + ": 成功Hook网络请求");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook网络请求失败: " + e.getMessage());
        }
    }

    /**
     * 判断URL是否是商品详情相关
     */
    private boolean isGoodsDetailUrl(String url) {
        if (url == null) return false;
        String lowerUrl = url.toLowerCase();
        return lowerUrl.contains("goods") || lowerUrl.contains("product") ||
               lowerUrl.contains("detail") || lowerUrl.contains("item") ||
               lowerUrl.contains("sku");
    }
    
    /**
     * 从Fragment中提取商品数据 - 基于运行结果的字段分析
     */
    private void extractFragmentGoodsData(Object fragment) {
        if (fragment == null) return;

        try {
            XposedBridge.log(TAG + ": 开始提取Fragment商品数据");

            // 基于运行结果，尝试获取关键字段
            String[] importantFields = {
                "goodsId", "goodsStatus", "W", "Q", "Y", "Z", // 运行结果中发现的字段
                "goodsEntity", "goodsResponse", "goodsData", "skuData"
            };

            Field[] fields = fragment.getClass().getDeclaredFields();
            for (Field field : fields) {
                try {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    Object value = field.get(fragment);

                    if (value != null && isImportantField(fieldName, importantFields)) {
                        XposedBridge.log(TAG + ": Fragment字段 " + fieldName + ": " + getValueDescription(value));

                        // 如果是复杂对象，进一步分析
                        if (isGoodsRelatedObject(value)) {
                            logGoodsInfo(value, "Fragment中的" + fieldName);
                        }
                    }
                } catch (Exception e) {
                    // 忽略无法访问的字段
                }
            }
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 提取Fragment数据失败: " + e.getMessage());
        }
    }

    /**
     * 判断字段是否重要
     */
    private boolean isImportantField(String fieldName, String[] importantFields) {
        for (String important : importantFields) {
            if (fieldName.equals(important)) {
                return true;
            }
        }

        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("goods") || lowerFieldName.contains("product") ||
               lowerFieldName.contains("sku") || lowerFieldName.contains("data") ||
               lowerFieldName.contains("entity") || lowerFieldName.contains("response");
    }
    
    /**
     * Hook重要的getter方法来获取商品数据
     */
    private void hookImportantGetters(Class<?> clazz, String className) {
        Method[] methods = clazz.getDeclaredMethods();
        int hookedCount = 0;
        int maxHooks = 10; // 限制Hook数量

        for (Method method : methods) {
            if (hookedCount >= maxHooks) break;

            String methodName = method.getName();
            if (methodName.startsWith("get") && method.getParameterTypes().length == 0) {
                if (isImportantGetter(methodName)) {
                    try {
                        XposedHelpers.findAndHookMethod(clazz, methodName, new XC_MethodHook() {
                            @Override
                            protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                                try {
                                    Object result = param.getResult();
                                    if (result != null) {
                                        String value = getValueDescription(result);
                                        XposedBridge.log(TAG + ": " + className + "." + methodName + "() = " + value);
                                    }
                                } catch (Exception e) {
                                    // 忽略getter内部的异常
                                }
                            }
                        });
                        hookedCount++;
                    } catch (Exception e) {
                        // 某些方法可能Hook失败，继续处理其他方法
                    }
                }
            }
        }

        XposedBridge.log(TAG + ": 在" + className + "中Hook了" + hookedCount + "个getter方法");
    }

    /**
     * Hook一个类的所有setter方法
     */
    private void hookAllSetters(Class<?> clazz, String className) {
        Method[] methods = clazz.getDeclaredMethods();
        int hookedCount = 0;
        int maxHooks = 10; // 限制Hook数量

        for (Method method : methods) {
            if (hookedCount >= maxHooks) break;

            String methodName = method.getName();
            if (methodName.startsWith("set") && method.getParameterTypes().length == 1) {
                if (isImportantSetter(methodName)) {
                    try {
                        XposedHelpers.findAndHookMethod(clazz, methodName,
                            method.getParameterTypes()[0], new XC_MethodHook() {
                                @Override
                                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                                    try {
                                        String value = getValueDescription(param.args[0]);
                                        XposedBridge.log(TAG + ": " + className + "." + methodName + "(" + value + ")");
                                    } catch (Exception e) {
                                        // 忽略setter内部的异常
                                    }
                                }
                            }
                        );
                        hookedCount++;
                    } catch (Exception e) {
                        // 某些方法可能Hook失败，继续处理其他方法
                    }
                }
            }
        }

        XposedBridge.log(TAG + ": 在" + className + "中Hook了" + hookedCount + "个setter方法");
    }

    /**
     * 判断是否是重要的getter方法
     */
    private boolean isImportantGetter(String methodName) {
        String lowerMethodName = methodName.toLowerCase();
        String[] importantKeywords = {
            "name", "title", "price", "url", "image", "desc", "id",
            "goods", "product", "category", "data", "info", "detail",
            "sku", "entity", "response"
        };

        for (String keyword : importantKeywords) {
            if (lowerMethodName.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否是重要的setter方法
     */
    private boolean isImportantSetter(String methodName) {
        String lowerMethodName = methodName.toLowerCase();
        String[] importantKeywords = {
            "name", "title", "price", "url", "image", "desc", "id",
            "goods", "product", "category", "data", "info", "detail",
            "sku", "entity", "response"
        };

        for (String keyword : importantKeywords) {
            if (lowerMethodName.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取值的描述
     */
    private String getValueDescription(Object value) {
        if (value == null) return "null";

        String valueStr = value.toString();
        String className = value.getClass().getSimpleName();

        // 截断过长的字符串
        if (valueStr.length() > 200) {
            valueStr = valueStr.substring(0, 200) + "...";
        }

        return className + "(" + valueStr + ")";
    }

    /**
     * 判断对象是否与商品相关
     */
    private boolean isGoodsRelatedObject(Object obj) {
        if (obj == null) return false;
        String className = obj.getClass().getName().toLowerCase();
        return className.contains("goods") || className.contains("product") ||
               className.contains("sku") || className.contains("entity") ||
               className.contains("response");
    }
    
    /**
     * 打印商品信息 - 增强版本
     */
    private void logGoodsInfo(Object goodsEntity, String source) {
        if (goodsEntity == null) return;

        try {
            StringBuilder sb = new StringBuilder();
            sb.append("\n========== ").append(source).append(" ==========\n");
            sb.append("商品对象类型: ").append(goodsEntity.getClass().getName()).append("\n");

            // 使用反射获取所有字段
            Field[] fields = goodsEntity.getClass().getDeclaredFields();
            int fieldCount = 0;

            for (Field field : fields) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(goodsEntity);
                    if (value != null && shouldLogField(field.getName(), value)) {
                        String valueDesc = getValueDescription(value);
                        sb.append(field.getName()).append(": ").append(valueDesc).append("\n");
                        fieldCount++;

                        // 限制输出字段数量，避免日志过长
                        if (fieldCount >= 20) {
                            sb.append("... (更多字段已省略)\n");
                            break;
                        }
                    }
                } catch (Exception e) {
                    // 忽略无法访问的字段
                }
            }

            sb.append("====================\n");

            String logMessage = sb.toString();
            XposedBridge.log(TAG + ": " + logMessage);
            Log.d(TAG, logMessage);

        } catch (Exception e) {
            XposedBridge.log(TAG + ": 打印商品信息失败: " + e.getMessage());
        }
    }

    /**
     * 判断是否应该记录这个字段
     */
    private boolean shouldLogField(String fieldName, Object value) {
        if (value == null) return false;

        String lowerFieldName = fieldName.toLowerCase();
        String[] importantKeywords = {
            "id", "name", "title", "price", "url", "image", "desc",
            "goods", "product", "sku", "category", "mall", "data",
            "entity", "response", "status"
        };

        for (String keyword : importantKeywords) {
            if (lowerFieldName.contains(keyword)) {
                return true;
            }
        }

        // 如果是基本数据类型且不为默认值，也记录
        if (value instanceof String && !((String) value).isEmpty()) {
            return true;
        }
        if (value instanceof Number && !value.equals(0) && !value.equals(0L) && !value.equals(0.0)) {
            return true;
        }
        if (value instanceof Boolean) {
            return true;
        }

        return false;
    }
    
}