2025-09-02 14:40:02,817 - INFO - 初始化监控器
2025-09-02 14:40:02,818 - INFO - Python监听端口: 9999
2025-09-02 14:40:02,818 - INFO - Xposed服务器: http://localhost:8888
2025-09-02 14:40:02,818 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:40:02,827 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:40:06,887 - ERROR - ❌ 无法连接到Xposed服务器: HTTPConnectionPool(host='localhost', port=8888): Max retries exceeded with url: /ping (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000215A0AB0440>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:40:06,887 - WARNING - ⚠️ 无法连接到Xposed模块HTTP服务器
2025-09-02 14:40:06,887 - INFO - 这不影响数据接收功能，但请确保:
2025-09-02 14:40:06,888 - INFO - 1. 拼多多App已启动
2025-09-02 14:40:06,888 - INFO - 2. Xposed模块已激活
2025-09-02 14:40:06,888 - INFO - 3. Xposed模块HTTP服务器正在运行
2025-09-02 14:40:06,889 - INFO - 开始监控循环
2025-09-02 14:40:06,889 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:40:06,889 - INFO - 🌐 Python HTTP服务器监听: http://localhost:9999
2025-09-02 14:40:06,889 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:40:06,890 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 14:40:09,796 - INFO - 收到停止信号
2025-09-02 14:40:09,796 - INFO - 🛑 停止监控
2025-09-02 14:40:09,926 - INFO - HTTP服务器已停止
2025-09-02 14:41:01,639 - INFO - 初始化监控器
2025-09-02 14:41:01,640 - INFO - Python监听端口: 9999
2025-09-02 14:41:01,640 - INFO - Xposed服务器: http://**************:8888
2025-09-02 14:41:01,640 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:41:01,647 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:41:01,785 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756795261642
2025-09-02 14:41:01,786 - INFO - 开始监控循环
2025-09-02 14:41:01,786 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:41:01,786 - INFO - 🌐 Python HTTP服务器监听: http://localhost:9999
2025-09-02 14:41:01,787 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:41:01,787 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 14:42:02,631 - INFO - 收到停止信号
2025-09-02 14:42:02,631 - INFO - 🛑 停止监控
2025-09-02 14:46:08,129 - INFO - 初始化监控器
2025-09-02 14:46:08,129 - INFO - Python监听端口: 9999
2025-09-02 14:46:08,129 - INFO - Xposed服务器: http://**************:8888
2025-09-02 14:46:08,129 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:46:08,136 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:46:10,597 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756795570466
2025-09-02 14:46:10,597 - INFO - 开始监控循环
2025-09-02 14:46:10,597 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:46:10,598 - INFO - 🌐 Python HTTP服务器监听: http://localhost:9999
2025-09-02 14:46:10,599 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:46:10,599 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 14:46:39,419 - INFO - 收到停止信号
2025-09-02 14:46:39,419 - INFO - 🛑 停止监控
