#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼多多数据监控脚本
实时监控Xposed模块发送的JSON数据
"""

import json
import time
import requests
import threading
from datetime import datetime
from typing import Dict, Any, Optional
import logging
from collections import defaultdict
import sys
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import socketserver

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('pdd_monitor.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class DataReceiveHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器，接收Xposed模块发送的数据"""

    def __init__(self, *args, monitor_instance=None, **kwargs):
        self.monitor_instance = monitor_instance
        super().__init__(*args, **kwargs)

    def do_POST(self):
        """处理POST请求"""
        try:
            # 获取请求路径
            path = urllib.parse.urlparse(self.path).path

            if path == '/data':
                # 读取POST数据
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length).decode('utf-8')

                # 解析JSON数据
                data_packet = json.loads(post_data)

                # 处理数据
                if self.monitor_instance:
                    self.monitor_instance.process_received_data(data_packet)

                # 返回成功响应
                response = {
                    'status': 'success',
                    'message': 'Data received',
                    'timestamp': int(time.time() * 1000)
                }

                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode('utf-8'))

            else:
                self.send_error(404, 'Not Found')

        except Exception as e:
            logger.error(f"处理POST请求异常: {e}")
            self.send_error(500, f'Internal Server Error: {str(e)}')

    def do_GET(self):
        """处理GET请求"""
        try:
            path = urllib.parse.urlparse(self.path).path

            if path == '/ping':
                response = {
                    'status': 'ok',
                    'server': 'PDDDataMonitor',
                    'timestamp': int(time.time() * 1000)
                }

                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode('utf-8'))

            elif path == '/status':
                if self.monitor_instance:
                    stats = self.monitor_instance.stats.copy()
                    stats['running'] = self.monitor_instance.running
                    stats['start_time'] = stats['start_time'].isoformat()
                    if stats['last_data_time']:
                        stats['last_data_time'] = stats['last_data_time'].isoformat()

                    # 转换defaultdict为普通dict
                    stats['by_parse_type'] = dict(stats['by_parse_type'])
                    stats['by_data_type'] = dict(stats['by_data_type'])

                    self.send_response(200)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps(stats, ensure_ascii=False).encode('utf-8'))
                else:
                    self.send_error(500, 'Monitor instance not available')
            else:
                self.send_error(404, 'Not Found')

        except Exception as e:
            logger.error(f"处理GET请求异常: {e}")
            self.send_error(500, f'Internal Server Error: {str(e)}')

    def log_message(self, format, *args):
        """重写日志方法，避免过多输出"""
        pass

class PDDDataMonitor:
    """拼多多数据监控器"""

    def __init__(self, listen_port=9999, xposed_host='localhost', xposed_port=8888, check_interval=2):
        # Python监听端口（接收Xposed模块数据）
        self.listen_port = listen_port

        # Xposed模块HTTP服务器地址（用于状态检查）
        self.xposed_host = xposed_host
        self.xposed_port = xposed_port
        self.xposed_url = f"http://{xposed_host}:{xposed_port}"

        self.check_interval = check_interval

        # HTTP服务器
        self.http_server = None
        self.server_thread = None
        
        # 统计信息
        self.stats = {
            'total_received': 0,
            'by_parse_type': defaultdict(int),
            'by_data_type': defaultdict(int),
            'start_time': datetime.now(),
            'last_data_time': None
        }
        
        # 数据去重
        self.seen_data = set()
        self.running = False
        
        logger.info(f"初始化监控器")
        logger.info(f"Python监听端口: {self.listen_port}")
        logger.info(f"Xposed服务器: {self.xposed_url}")

    def start_http_server(self):
        """启动HTTP服务器接收数据"""
        try:
            # 创建处理器工厂，传入monitor实例
            def handler_factory(*args, **kwargs):
                return DataReceiveHandler(*args, monitor_instance=self, **kwargs)

            self.http_server = HTTPServer(('localhost', self.listen_port), handler_factory)

            # 在单独线程中运行服务器
            self.server_thread = threading.Thread(target=self.http_server.serve_forever, daemon=True)
            self.server_thread.start()

            logger.info(f"✅ HTTP服务器启动成功，监听端口: {self.listen_port}")
            return True

        except Exception as e:
            logger.error(f"❌ HTTP服务器启动失败: {e}")
            return False

    def stop_http_server(self):
        """停止HTTP服务器"""
        if self.http_server:
            self.http_server.shutdown()
            self.http_server.server_close()
            logger.info("HTTP服务器已停止")

    def check_xposed_server_status(self) -> bool:
        """检查Xposed服务器状态"""
        try:
            response = requests.get(f"{self.xposed_url}/ping", timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ Xposed服务器在线 - 端口: {data.get('port')}, 时间: {data.get('timestamp')}")
                return True
            else:
                logger.warning(f"❌ Xposed服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 无法连接到Xposed服务器: {e}")
            return False

    def get_xposed_server_status(self) -> Optional[Dict[str, Any]]:
        """获取Xposed服务器详细状态"""
        try:
            response = requests.get(f"{self.xposed_url}/status", timeout=5)
            if response.status_code == 200:
                return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"获取Xposed服务器状态失败: {e}")
        return None
    
    def format_json_data(self, json_str: str, max_length: int = 500) -> str:
        """格式化JSON数据用于显示"""
        try:
            # 尝试解析JSON并美化
            parsed = json.loads(json_str)
            formatted = json.dumps(parsed, ensure_ascii=False, indent=2)
            
            if len(formatted) > max_length:
                return formatted[:max_length] + "\n... (数据已截断)"
            return formatted
        except json.JSONDecodeError:
            # 如果不是有效JSON，直接截断显示
            if len(json_str) > max_length:
                return json_str[:max_length] + "... (数据已截断)"
            return json_str
    
    def extract_key_info(self, json_str: str) -> Dict[str, Any]:
        """提取JSON中的关键信息"""
        key_info = {}
        try:
            data = json.loads(json_str)
            
            # 提取商品相关信息
            if isinstance(data, dict):
                # 商品ID
                for key in ['goods_id', 'goodsId', 'product_id', 'productId', 'item_id', 'itemId']:
                    if key in data:
                        key_info['商品ID'] = data[key]
                        break
                
                # 商品名称
                for key in ['goods_name', 'goodsName', 'product_name', 'productName', 'item_name', 'title']:
                    if key in data:
                        key_info['商品名称'] = data[key]
                        break
                
                # 价格信息
                for key in ['price', 'single_price', 'market_price', 'goods_price']:
                    if key in data:
                        key_info['价格'] = data[key]
                        break
                
                # 图片URL
                for key in ['thumb_url', 'image_url', '_oak_gallery', 'cover_url']:
                    if key in data:
                        key_info['图片'] = data[key]
                        break
                
                # 页面信息
                for key in ['page_type', 'page_url', 'page_from']:
                    if key in data:
                        key_info[key] = data[key]
                
                # 商店信息
                if 'mall_id' in data:
                    key_info['商店ID'] = data['mall_id']
                
        except (json.JSONDecodeError, TypeError):
            pass
        
        return key_info
    
    def print_data_info(self, data_packet: Dict[str, Any]):
        """打印数据信息"""
        timestamp = datetime.fromtimestamp(data_packet.get('timestamp', 0) / 1000)
        parse_type = data_packet.get('parseType', 'Unknown')
        data_type = data_packet.get('dataType', 'Unknown')
        counter = data_packet.get('counter', 0)
        json_data = data_packet.get('jsonData', '')
        
        print("\n" + "="*80)
        print(f"🎯 数据接收 #{counter}")
        print(f"⏰ 时间: {timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        print(f"🔧 解析类型: {parse_type}")
        print(f"📦 数据类型: {data_type}")
        
        # 提取关键信息
        key_info = self.extract_key_info(json_data)
        if key_info:
            print("🔑 关键信息:")
            for key, value in key_info.items():
                print(f"   {key}: {value}")
        
        print("📄 完整JSON数据:")
        formatted_json = self.format_json_data(json_data)
        print(formatted_json)
        print("="*80)
    
    def process_received_data(self, data_packet: Dict[str, Any]):
        """处理接收到的数据"""
        try:
            # 数据去重
            json_data = data_packet.get('jsonData', '')
            data_hash = hash(json_data)
            
            if data_hash in self.seen_data:
                logger.debug("跳过重复数据")
                return
            
            self.seen_data.add(data_hash)
            
            # 限制去重集合大小
            if len(self.seen_data) > 10000:
                self.seen_data.clear()
                logger.info("清理去重缓存")
            
            # 更新统计信息
            self.stats['total_received'] += 1
            self.stats['by_parse_type'][data_packet.get('parseType', 'Unknown')] += 1
            self.stats['by_data_type'][data_packet.get('dataType', 'Unknown')] += 1
            self.stats['last_data_time'] = datetime.now()
            
            # 打印数据信息
            self.print_data_info(data_packet)
            
            # 可以在这里添加更多的数据处理逻辑
            # 比如保存到文件、数据库等
            
        except Exception as e:
            logger.error(f"处理数据异常: {e}")
    
    def print_stats(self):
        """打印统计信息"""
        runtime = datetime.now() - self.stats['start_time']
        
        print("\n" + "📊 统计信息 " + "="*60)
        print(f"运行时间: {runtime}")
        print(f"总接收数据: {self.stats['total_received']}")
        print(f"最后数据时间: {self.stats['last_data_time']}")
        
        if self.stats['by_parse_type']:
            print("按解析类型统计:")
            for parse_type, count in self.stats['by_parse_type'].items():
                print(f"  {parse_type}: {count}")
        
        if self.stats['by_data_type']:
            print("按数据类型统计:")
            for data_type, count in self.stats['by_data_type'].items():
                print(f"  {data_type}: {count}")
        
        print("="*70)
    
    def monitor_loop(self):
        """监控循环"""
        logger.info("开始监控循环")

        while self.running:
            try:
                # 检查Xposed服务器状态
                status = self.get_xposed_server_status()
                if status:
                    logger.debug(f"Xposed服务器状态: 数据计数={status.get('dataCount')}")

                time.sleep(self.check_interval)

            except KeyboardInterrupt:
                logger.info("收到中断信号，停止监控")
                break
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(5)
    
    def start_monitoring(self):
        """开始监控"""
        logger.info("🚀 启动拼多多数据监控器")

        # 启动HTTP服务器接收数据
        if not self.start_http_server():
            logger.error("❌ 无法启动HTTP服务器")
            return False

        # 检查Xposed服务器连接（可选，不影响主要功能）
        if not self.check_xposed_server_status():
            logger.warning("⚠️ 无法连接到Xposed模块HTTP服务器")
            logger.info("这不影响数据接收功能，但请确保:")
            logger.info("1. 拼多多App已启动")
            logger.info("2. Xposed模块已激活")
            logger.info("3. Xposed模块HTTP服务器正在运行")

        self.running = True

        # 启动监控线程
        monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        monitor_thread.start()
        
        try:
            logger.info("📱 监控已启动，等待数据...")
            logger.info(f"🌐 Python HTTP服务器监听: http://localhost:{self.listen_port}")
            logger.info("💡 提示: 在拼多多App中浏览商品以触发数据捕获")
            logger.info("⌨️  按 Ctrl+C 停止监控")

            # 定期打印统计信息
            last_stats_time = time.time()

            while self.running:
                time.sleep(1)

                # 每30秒打印一次统计信息
                if time.time() - last_stats_time > 30:
                    self.print_stats()
                    last_stats_time = time.time()

        except KeyboardInterrupt:
            logger.info("收到停止信号")
        finally:
            self.stop_monitoring()

        return True

    def stop_monitoring(self):
        """停止监控"""
        logger.info("🛑 停止监控")
        self.running = False

        # 停止HTTP服务器
        self.stop_http_server()

        # 打印最终统计信息
        self.print_stats()

def main():
    """主函数"""
    print("🎯 拼多多数据监控器 v1.0")
    print("=" * 50)
    
    # 创建监控器
    monitor = PDDDataMonitor()
    
    # 开始监控
    monitor.start_monitoring()

if __name__ == "__main__":
    main()
