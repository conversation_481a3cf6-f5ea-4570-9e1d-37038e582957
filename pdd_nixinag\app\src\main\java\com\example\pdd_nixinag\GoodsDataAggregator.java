package com.example.pdd_nixinag;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.robv.android.xposed.XposedBridge;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 商品数据聚合器
 * 负责收集、去重和聚合商品数据，定期发送给Python脚本
 */
public class GoodsDataAggregator {
    
    private static final String TAG = "GoodsDataAggregator";
    private final Gson gson = new Gson();
    private final JsonParser jsonParser = new JsonParser();
    
    // 商品数据存储 - 使用goods_id作为key去重
    private final ConcurrentHashMap<String, JsonObject> goodsDataMap = new ConcurrentHashMap<>();
    
    // 数据发送器
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final AtomicInteger aggregatedCounter = new AtomicInteger(0);
    
    // 配置参数
    private static final int SEND_INTERVAL_SECONDS = 5; // 每5秒发送一次聚合数据
    private static final int MAX_GOODS_COUNT = 50; // 最多保存50个商品数据
    
    public GoodsDataAggregator() {
        // 启动定时发送任务
        startScheduledSender();
        XposedBridge.log(TAG + ": 商品数据聚合器已启动，发送间隔: " + SEND_INTERVAL_SECONDS + "秒");
    }
    
    /**
     * 添加商品数据
     */
    public void addGoodsData(String jsonData, String parseType, String dataType) {
        try {
            JsonObject jsonObject = jsonParser.parse(jsonData).getAsJsonObject();
            
            // 提取商品ID作为唯一标识
            String goodsId = extractGoodsId(jsonObject);
            if (goodsId == null) {
                XposedBridge.log(TAG + ": 无法提取商品ID，跳过数据");
                return;
            }
            
            // 检查是否已存在相同商品数据
            if (goodsDataMap.containsKey(goodsId)) {
                // 合并数据而不是替换
                JsonObject existingData = goodsDataMap.get(goodsId);
                JsonObject mergedData = mergeGoodsData(existingData, jsonObject, parseType, dataType);
                goodsDataMap.put(goodsId, mergedData);
                XposedBridge.log(TAG + ": 更新商品数据: " + goodsId);
            } else {
                // 添加新商品数据
                JsonObject goodsData = createGoodsDataEntry(jsonObject, parseType, dataType);
                goodsDataMap.put(goodsId, goodsData);
                XposedBridge.log(TAG + ": 新增商品数据: " + goodsId);
                
                // 如果数据过多，清理旧数据
                if (goodsDataMap.size() > MAX_GOODS_COUNT) {
                    cleanupOldData();
                }
            }
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 处理商品数据异常: " + e.getMessage());
        }
    }
    
    /**
     * 提取商品ID
     */
    private String extractGoodsId(JsonObject jsonObject) {
        // 尝试多种可能的商品ID字段
        String[] idFields = {"goods_id", "goodsId", "product_id", "productId", "item_id", "itemId"};
        
        for (String field : idFields) {
            if (jsonObject.has(field)) {
                return jsonObject.get(field).getAsString();
            }
        }
        
        return null;
    }
    
    /**
     * 创建商品数据条目
     */
    private JsonObject createGoodsDataEntry(JsonObject originalData, String parseType, String dataType) {
        JsonObject entry = new JsonObject();
        entry.addProperty("goods_id", extractGoodsId(originalData));
        entry.addProperty("first_seen", System.currentTimeMillis());
        entry.addProperty("last_updated", System.currentTimeMillis());
        entry.addProperty("parse_type", parseType);
        entry.addProperty("data_type", dataType);
        entry.addProperty("update_count", 1);
        entry.add("data", originalData);
        
        return entry;
    }
    
    /**
     * 合并商品数据
     */
    private JsonObject mergeGoodsData(JsonObject existing, JsonObject newData, String parseType, String dataType) {
        existing.addProperty("last_updated", System.currentTimeMillis());
        existing.addProperty("update_count", existing.get("update_count").getAsInt() + 1);
        
        // 如果新数据更完整，则更新
        JsonObject existingData = existing.getAsJsonObject("data");
        JsonObject mergedData = mergeJsonObjects(existingData, newData);
        existing.add("data", mergedData);
        
        return existing;
    }
    
    /**
     * 合并两个JSON对象
     */
    private JsonObject mergeJsonObjects(JsonObject obj1, JsonObject obj2) {
        JsonObject merged = obj1.deepCopy();
        
        obj2.entrySet().forEach(entry -> {
            String key = entry.getKey();
            if (!merged.has(key) || merged.get(key).isJsonNull()) {
                merged.add(key, entry.getValue());
            }
        });
        
        return merged;
    }
    
    /**
     * 清理旧数据
     */
    private void cleanupOldData() {
        if (goodsDataMap.size() <= MAX_GOODS_COUNT) return;
        
        // 找到最旧的数据并删除
        String oldestKey = null;
        long oldestTime = Long.MAX_VALUE;
        
        for (String key : goodsDataMap.keySet()) {
            JsonObject data = goodsDataMap.get(key);
            long firstSeen = data.get("first_seen").getAsLong();
            if (firstSeen < oldestTime) {
                oldestTime = firstSeen;
                oldestKey = key;
            }
        }
        
        if (oldestKey != null) {
            goodsDataMap.remove(oldestKey);
            XposedBridge.log(TAG + ": 清理旧数据: " + oldestKey);
        }
    }
    
    /**
     * 启动定时发送任务
     */
    private void startScheduledSender() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                sendAggregatedData();
            } catch (Exception e) {
                XposedBridge.log(TAG + ": 定时发送异常: " + e.getMessage());
            }
        }, SEND_INTERVAL_SECONDS, SEND_INTERVAL_SECONDS, TimeUnit.SECONDS);
    }
    
    /**
     * 发送聚合数据
     */
    private void sendAggregatedData() {
        if (goodsDataMap.isEmpty()) {
            return;
        }
        
        try {
            // 创建聚合数据包
            JsonObject aggregatedPacket = new JsonObject();
            aggregatedPacket.addProperty("type", "aggregated_goods_data");
            aggregatedPacket.addProperty("timestamp", System.currentTimeMillis());
            aggregatedPacket.addProperty("counter", aggregatedCounter.incrementAndGet());
            aggregatedPacket.addProperty("goods_count", goodsDataMap.size());
            aggregatedPacket.addProperty("package_name", "com.xunmeng.pinduoduo");
            
            // 添加所有商品数据
            JsonObject goodsData = new JsonObject();
            for (String goodsId : goodsDataMap.keySet()) {
                goodsData.add(goodsId, goodsDataMap.get(goodsId));
            }
            aggregatedPacket.add("goods_data", goodsData);
            
            String packetJson = gson.toJson(aggregatedPacket);
            
            // 发送数据
            PDDJsonDataHook.sendAggregatedData(packetJson);
            
            XposedBridge.log(TAG + ": 📦 发送聚合数据，包含 " + goodsDataMap.size() + " 个商品");
            
            // 发送后清空数据（可选，根据需求决定）
            // goodsDataMap.clear();
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 发送聚合数据异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前统计信息
     */
    public String getStats() {
        return "商品数据统计: 当前商品数=" + goodsDataMap.size() + 
               ", 已发送批次=" + aggregatedCounter.get();
    }
    
    /**
     * 停止聚合器
     */
    public void shutdown() {
        scheduler.shutdown();
        XposedBridge.log(TAG + ": 商品数据聚合器已停止");
    }
}
