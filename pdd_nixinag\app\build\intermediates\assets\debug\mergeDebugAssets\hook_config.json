{"version": "2.0.0", "description": "拼多多Hook模块配置文件 - 基于运行结果优化", "last_updated": "2025-09-02", "global_settings": {"enable_logging": true, "log_level": "INFO", "max_log_length": 1000, "enable_performance_monitoring": false, "hook_delay_ms": 100}, "modules": {"PDDGoodsDetailHook": {"enabled": true, "priority": 1, "description": "专项商品详情数据捕获", "hooks": {"goods_response_creation": {"enabled": true, "class": "com.xunmeng.pinduoduo.goods.entity.GoodsResponse", "methods": ["<init>"]}, "product_detail_fragment": {"enabled": true, "class": "com.xunmeng.pinduoduo.goods.ProductDetailFragment", "methods": ["onViewCreated"], "delay_extraction_ms": 1000}, "goods_view_model": {"enabled": true, "class": "com.xunmeng.pinduoduo.goods.GoodsViewModel", "methods": ["*goods*", "*data*", "*response*", "*entity*"]}, "sku_data_provider": {"enabled": true, "class": "com.xunmeng.pinduoduo.goods.model.GoodsDetailSkuDataProvider", "methods": ["<init>"]}, "network_requests": {"enabled": true, "class": "okhttp3.Response", "methods": ["body"], "url_filters": ["goods", "product", "detail", "item", "sku"]}}, "field_filters": {"critical_fields": ["goodsId", "goodsStatus", "W", "Q", "Y", "Z", "goodsEntity", "goodsResponse", "goodsData", "sku<PERSON><PERSON>", "goodsViewModel", "dataProvider"], "important_keywords": ["id", "name", "title", "price", "url", "image", "desc", "goods", "product", "sku", "category", "mall", "data", "entity", "response", "status", "count", "thumb", "pic", "brand", "spec", "attr", "tag", "sales", "stock"]}}, "PDDJsonDataHook": {"enabled": true, "priority": 2, "description": "JSON数据解析拦截", "hooks": {"gson_parsing": {"enabled": true, "class": "com.google.gson.Gson", "methods": ["fromJson"], "max_json_length": 800}, "json_object_parsing": {"enabled": true, "class": "org.json.JSONObject", "methods": ["<init>"], "max_json_length": 600}, "fastjson_parsing": {"enabled": true, "class": "com.alibaba.fastjson.JSON", "methods": ["parseObject"], "max_json_length": 800}, "custom_json_parsing": {"enabled": true, "classes": ["com.xunmeng.pinduoduo.basekit.util.JSONFormatUtils", "com.xunmeng.pinduoduo.util.JsonUtil", "com.xunmeng.pinduoduo.common.JsonParser", "com.xunmeng.pinduoduo.network.JsonResponseParser"], "method_patterns": ["*parse*", "*from*", "*decode*", "*json*"], "max_json_length": 600}}, "json_filters": {"goods_keywords": ["goods_id", "goodsid", "product_id", "productid", "item_id", "itemid", "goods_name", "goodsname", "product_name", "productname", "item_name", "goods_price", "product_price", "item_price", "price", "sku_id", "goods_detail", "product_detail", "item_detail", "goods_info", "thumb_url", "image_url", "gallery", "spec", "attribute", "category_id", "mall_id", "shop_id", "brand", "sales", "stock", "inventory"], "min_keyword_matches": 2, "min_json_length": 50}}, "PDDEnhancedHookModule": {"enabled": true, "priority": 3, "description": "增强网络和JSON Hook", "hooks": {"network_monitoring": {"enabled": true, "response_size_limit": 10240, "url_patterns": ["api", "goods", "product", "mall"]}, "json_response_parsing": {"enabled": true, "auto_parse": true, "save_raw_response": false}}}, "PDDAdvancedHookModule": {"enabled": true, "priority": 4, "description": "高级Fragment和Activity监控", "hooks": {"fragment_lifecycle": {"enabled": true, "methods": ["onViewCreated", "onResume", "onStart"]}, "activity_lifecycle": {"enabled": true, "methods": ["onCreate", "onResume"]}}}}, "target_classes": {"goods_entities": ["com.xunmeng.pinduoduo.goods.entity.GoodsEntity", "com.xunmeng.pinduoduo.goods.entity.GoodsResponse", "com.xunmeng.pinduoduo.goods.entity.GoodsDetailEntity", "com.xunmeng.pinduoduo.goods.entity.SkuEntity"], "fragments": ["com.xunmeng.pinduoduo.goods.ProductDetailFragment", "com.xunmeng.pinduoduo.goods.fragment.GoodsDetailFragment", "com.xunmeng.pinduoduo.search.fragment.SearchResultFragment"], "activities": ["com.xunmeng.pinduoduo.goods.gallery.GoodsDetailGalleryActivity", "com.xunmeng.pinduoduo.goods.GoodsDetailActivity"], "data_providers": ["com.xunmeng.pinduoduo.goods.model.GoodsDetailSkuDataProvider", "com.xunmeng.pinduoduo.goods.model.GoodsDataProvider"], "view_models": ["com.xunmeng.pinduoduo.goods.GoodsViewModel", "com.xunmeng.pinduoduo.goods.GoodsDetailViewModel"]}, "api_endpoints": {"goods_detail": ["/api/goods/detail", "/api/product/info", "/api/item/detail", "/goods/detail", "/product/detail"], "goods_list": ["/api/goods/list", "/api/search/goods", "/api/category/goods"]}, "output_settings": {"log_format": "detailed", "include_timestamp": true, "include_thread_info": false, "max_field_count": 25, "truncate_long_values": true, "value_max_length": 150, "export_format": "json", "auto_export": false}, "performance_settings": {"max_hooks_per_class": 10, "hook_timeout_ms": 5000, "enable_hook_statistics": false, "memory_usage_monitoring": false}, "debug_settings": {"enable_debug_mode": false, "verbose_logging": false, "log_all_method_calls": false, "save_debug_info": false}, "compatibility": {"min_android_version": 21, "target_pdd_versions": ["6.0.0", "6.1.0", "6.2.0"], "xposed_framework": ["LSPosed", "EdXposed", "Xposed"], "fallback_mode": true}}