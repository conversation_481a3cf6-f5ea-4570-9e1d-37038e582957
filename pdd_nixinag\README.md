# 拼多多Xposed Hook模块

## 功能说明

本模块用于Hook拼多多App，捕获并打印商品详情数据与商品分类列表数据。

### 主要功能
- 🛍️ 捕获商品详情页面数据
- 📂 捕获商品分类列表数据  
- 🔍 捕获搜索结果中的商品信息
- 🌐 监控商品相关的网络请求
- 📄 解析JSON格式的商品/分类数据

## 安装步骤

### 1. 环境准备
- Android设备（已Root）
- 安装Xposed Framework（推荐LSPosed）
- 安装拼多多App

### 2. 编译模块
```bash
# 进入项目目录
cd pdd_nixinag

# 运行构建脚本
build.bat

# 或手动构建
gradlew assembleDebug
```

### 3. 安装模块
1. 安装生成的`pdd_hook.apk`文件
2. 在Xposed管理器中启用"拼多多Hook模块"
3. 重启手机或重启拼多多应用

## 使用方法

### 查看日志输出

#### 通过adb命令
```bash
# 查看Xposed日志
adb logcat | grep "PDDHook"

# 查看系统日志  
adb logcat -s "PDDHook"

# 实时监控
adb logcat | findstr "商品\|分类\|PDDHook"
```

#### 通过Xposed日志
- 打开LSPosed管理器
- 选择"拼多多Hook模块" 
- 查看模块日志

### 触发数据捕获

1. **商品详情数据**
   - 打开拼多多App
   - 进入任意商品详情页面
   - 查看日志输出商品详情信息

2. **分类数据**
   - 进入拼多多首页或商城页面
   - 浏览分类相关页面
   - 查看日志输出分类信息

3. **搜索结果数据**
   - 在拼多多中搜索商品
   - 查看搜索结果页面
   - 查看日志输出搜索结果中的商品信息

## 日志格式说明

### 商品数据日志
```
==================================================
🛍️ 商品数据: [数据来源]
📦 对象类型: [Java类名]
⏰ 捕获时间: [时间戳]
------------------------------
  📋 goodsId: [商品ID]
  📋 goodsName: [商品名称]  
  📋 price: [价格]
  📋 imageUrl: [图片URL]
  ...
==================================================
```

### 分类数据日志
```
==================================================
📂 分类数据: [数据来源]
📁 对象类型: [Java类名]
⏰ 捕获时间: [时间戳]
------------------------------
  🗂️ categoryId: [分类ID]
  🗂️ categoryName: [分类名称]
  🗂️ parentId: [父分类ID]
  ...
==================================================
```

### 网络请求日志
```
========================================
🌐 网络请求: [请求方法]
🔗 URL: [请求URL]
⏰ 时间: [时间戳]  
📄 数据: [请求/响应数据]
========================================
```

## Hook目标

### 主要Hook类
- `GoodsDetailGalleryActivity` - 商品详情页面
- `GoodsEntity` - 商品实体类
- `SearchResultGoodsNewFragment` - 搜索结果页面
- `MallFragment` - 商城页面
- OkHttp网络请求相关类

### Hook方法
- `onCreate`, `onViewCreated` - 页面生命周期
- `setData`, `loadData` - 数据设置和加载
- `fromJson`, `toJson` - JSON解析
- HTTP请求相关方法

## 故障排除

### 常见问题

1. **模块未生效**
   - 确认Xposed Framework正常工作
   - 确认模块已在Xposed管理器中启用
   - 重启拼多多App或重启手机

2. **无日志输出**
   - 检查adb连接是否正常
   - 确认拼多多App版本匹配
   - 检查Hook目标类是否存在

3. **应用崩溃**
   - 查看详细错误日志
   - 可能是拼多多版本不兼容
   - 尝试禁用部分Hook功能

### 调试建议
- 使用`adb logcat`实时查看日志
- 注意日志中的错误信息
- 必要时可修改Hook目标类名

## 技术说明

### Hook框架
- 基于Xposed Framework
- 兼容LSPosed
- 支持Android 8.0+

### 目标应用  
- 拼多多 (com.xunmeng.pinduoduo)
- 测试版本: 7.73.0
- 理论支持其他版本

### 开发语言
- Java 11
- Android SDK 36

## 免责声明

本工具仅用于学习和研究目的，请遵守相关法律法规和平台服务条款。
使用者需承担使用本工具产生的一切后果和法律责任。

## 更新日志

### v1.0.0 (2024-09-01)
- 初始版本发布
- 支持商品详情数据捕获
- 支持分类数据捕获  
- 支持网络请求监控
- 支持JSON数据解析