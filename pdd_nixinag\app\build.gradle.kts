plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace = "com.example.pdd_nixinag"
    compileSdk = 36

    defaultConfig {
        applicationId = "com.example.pdd_nixinag"
        minSdk = 29
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
}

dependencies {

    implementation(libs.appcompat)
    implementation(libs.material)
    testImplementation(libs.junit)
    androidTestImplementation(libs.ext.junit)
    androidTestImplementation(libs.espresso.core)

    // Xposed API - 使用本地jar文件
    compileOnly(files("libs/XposedBridgeApi-54.jar"))

    // NanoHTTPD for HTTP server
    implementation("org.nanohttpd:nanohttpd:2.3.1")

    // JSON processing
    implementation("com.google.code.gson:gson:2.10.1")
}