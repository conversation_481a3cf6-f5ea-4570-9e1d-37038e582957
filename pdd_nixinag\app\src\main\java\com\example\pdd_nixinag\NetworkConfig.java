package com.example.pdd_nixinag;

/**
 * 网络配置类
 * 用于管理IP地址和端口配置
 */
public class NetworkConfig {
    
    // 电脑IP地址 - 需要根据实际网络环境修改
    public static final String COMPUTER_IP = "**************";
    
    // Python监控脚本端口
    public static final int PYTHON_MONITOR_PORT = 9999;
    
    // Xposed模块HTTP服务器端口（起始端口）
    public static final int XPOSED_SERVER_PORT = 8888;
    
    /**
     * 获取Python监控脚本的完整URL
     */
    public static String getPythonMonitorUrl() {
        return "http://" + COMPUTER_IP + ":" + PYTHON_MONITOR_PORT + "/data";
    }
    
    /**
     * 获取Xposed服务器的完整URL
     */
    public static String getXposedServerUrl(int port) {
        return "http://localhost:" + port + "/data";
    }
    
    /**
     * 检查IP地址是否为默认值
     */
    public static boolean isDefaultIP() {
        return "**************".equals(COMPUTER_IP);
    }
    
    /**
     * 获取配置信息
     */
    public static String getConfigInfo() {
        return "网络配置:\n" +
               "电脑IP: " + COMPUTER_IP + "\n" +
               "Python端口: " + PYTHON_MONITOR_PORT + "\n" +
               "Xposed端口: " + XPOSED_SERVER_PORT + "\n" +
               "Python URL: " + getPythonMonitorUrl();
    }
}
