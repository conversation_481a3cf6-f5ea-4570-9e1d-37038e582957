@echo off
chcp 65001 >nul
title 拼多多数据监控器

echo.
echo ========================================
echo    拼多多数据监控器 - 快速启动
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查requests库是否安装
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装requests库...
    pip install requests
    if errorlevel 1 (
        echo ❌ 安装requests失败，请手动执行: pip install requests
        pause
        exit /b 1
    )
    echo ✅ requests库安装成功
)

:: 检查监控脚本是否存在
if not exist "pdd_data_monitor.py" (
    echo ❌ 错误: 未找到pdd_data_monitor.py文件
    echo 请确保脚本文件在当前目录下
    pause
    exit /b 1
)

echo.
echo 🚀 启动拼多多数据监控器...
echo.
echo 💡 使用提示:
echo    1. 确保拼多多App已安装Xposed模块
echo    2. 在拼多多App中浏览商品以触发数据捕获
echo    3. 按 Ctrl+C 停止监控
echo.
echo ========================================
echo.

:: 启动Python监控脚本
python pdd_data_monitor.py

echo.
echo 监控已停止
pause
