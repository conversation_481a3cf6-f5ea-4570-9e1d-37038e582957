package com.example.pdd_nixinag;

import android.util.Log;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage.LoadPackageParam;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

/**
 * 专门针对商品详情数据获取的增强Hook模块
 * 基于运行结果分析优化
 */
public class PDDGoodsDetailHook implements IXposedHookLoadPackage {

    private static final String TAG = "PDDGoodsDetailHook";
    private static final String PDD_PACKAGE = "com.xunmeng.pinduoduo";
    private static final String MODULE_NAME = "PDDGoodsDetailHook";

    private HookConfigManager configManager;

    @Override
    public void handleLoadPackage(LoadPackageParam lpparam) throws Throwable {
        if (!PDD_PACKAGE.equals(lpparam.packageName)) {
            return;
        }

        // 初始化配置管理器
        configManager = HookConfigManager.getInstance();

        // 检查模块是否启用
        if (!configManager.isModuleEnabled(MODULE_NAME)) {
            XposedBridge.log(TAG + ": 模块已禁用，跳过Hook");
            return;
        }

        XposedBridge.log(TAG + ": 开始专项Hook商品详情数据 - 配置版本: " + configManager.getConfigVersion());
        XposedBridge.log(TAG + ": " + configManager.getConfigSummary());

        // 基于配置的精准Hook
        if (configManager.isHookEnabled(MODULE_NAME, "goods_response_creation")) {
            hookGoodsResponseCreation(lpparam);
        }
        if (configManager.isHookEnabled(MODULE_NAME, "product_detail_fragment")) {
            hookProductDetailFragmentData(lpparam);
        }
        if (configManager.isHookEnabled(MODULE_NAME, "goods_view_model")) {
            hookGoodsViewModelData(lpparam);
        }
        if (configManager.isHookEnabled(MODULE_NAME, "sku_data_provider")) {
            hookSkuDataProvider(lpparam);
        }
        if (configManager.isHookEnabled(MODULE_NAME, "network_requests")) {
            hookGoodsDetailAPI(lpparam);
        }

        XposedBridge.log(TAG + ": 商品详情Hook注入完成");
    }
    
    /**
     * Hook GoodsResponse创建 - 运行结果显示这是关键数据类
     */
    private void hookGoodsResponseCreation(LoadPackageParam lpparam) {
        try {
            String className = configManager.getHookClassName(MODULE_NAME, "goods_response_creation");
            if (className.isEmpty()) {
                className = "com.xunmeng.pinduoduo.goods.entity.GoodsResponse";
            }

            Class<?> goodsResponseClass = XposedHelpers.findClass(className, lpparam.classLoader);

            // Hook构造方法
            XposedHelpers.findAndHookConstructor(goodsResponseClass, new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    if (configManager.isLoggingEnabled()) {
                        XposedBridge.log(TAG + ": ✅ GoodsResponse对象已创建");
                        logDetailedGoodsData(param.thisObject, "GoodsResponse构造");
                    }
                }
            });

            XposedBridge.log(TAG + ": 成功Hook GoodsResponse构造方法: " + className);
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook GoodsResponse失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook ProductDetailFragment数据加载 - 运行结果的核心Fragment
     */
    private void hookProductDetailFragmentData(LoadPackageParam lpparam) {
        try {
            String className = configManager.getHookClassName(MODULE_NAME, "product_detail_fragment");
            if (className.isEmpty()) {
                className = "com.xunmeng.pinduoduo.goods.ProductDetailFragment";
            }

            Class<?> fragmentClass = XposedHelpers.findClass(className, lpparam.classLoader);

            // Hook onViewCreated - 运行结果显示这里有关键数据
            XposedHelpers.findAndHookMethod(fragmentClass, "onViewCreated",
                android.view.View.class, android.os.Bundle.class, new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        if (configManager.isLoggingEnabled()) {
                            XposedBridge.log(TAG + ": ✅ ProductDetailFragment视图已创建");
                        }

                        // 使用配置的延迟时间
                        int delayMs = configManager.getHookDelayMs();
                        android.os.Handler handler = new android.os.Handler();
                        handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                extractFragmentGoodsData(param.thisObject);
                            }
                        }, delayMs);
                    }
                }
            );

            XposedBridge.log(TAG + ": 成功Hook ProductDetailFragment: " + className);
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook ProductDetailFragment失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook GoodsViewModel - 运行结果中发现的数据管理类
     */
    private void hookGoodsViewModelData(LoadPackageParam lpparam) {
        try {
            Class<?> viewModelClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.goods.GoodsViewModel", 
                lpparam.classLoader
            );
            
            // Hook所有可能的数据设置方法
            Method[] methods = viewModelClass.getDeclaredMethods();
            for (Method method : methods) {
                String methodName = method.getName();
                if (methodName.contains("goods") || methodName.contains("data") || 
                    methodName.contains("response") || methodName.contains("entity")) {
                    
                    try {
                        XposedHelpers.findAndHookMethod(viewModelClass, methodName, 
                            method.getParameterTypes(), new XC_MethodHook() {
                                @Override
                                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                                    XposedBridge.log(TAG + ": ✅ GoodsViewModel." + methodName + " 已调用");
                                    
                                    // 记录参数和返回值
                                    if (param.args != null && param.args.length > 0) {
                                        for (int i = 0; i < param.args.length; i++) {
                                            if (param.args[i] != null && isGoodsRelatedObject(param.args[i])) {
                                                logDetailedGoodsData(param.args[i], "GoodsViewModel参数[" + i + "]");
                                            }
                                        }
                                    }
                                    
                                    Object result = param.getResult();
                                    if (result != null && isGoodsRelatedObject(result)) {
                                        logDetailedGoodsData(result, "GoodsViewModel返回值");
                                    }
                                }
                            }
                        );
                    } catch (Exception e) {
                        // 某些方法可能Hook失败，继续处理其他方法
                    }
                }
            }
            
            XposedBridge.log(TAG + ": 成功Hook GoodsViewModel");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook GoodsViewModel失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook SKU数据提供者 - 运行结果中的关键数据源
     */
    private void hookSkuDataProvider(LoadPackageParam lpparam) {
        try {
            Class<?> skuProviderClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.goods.model.GoodsDetailSkuDataProvider", 
                lpparam.classLoader
            );
            
            // Hook构造方法
            XposedHelpers.findAndHookConstructor(skuProviderClass, 
                java.lang.ref.WeakReference.class, new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        XposedBridge.log(TAG + ": ✅ GoodsDetailSkuDataProvider已创建");
                        
                        // 尝试从WeakReference中获取Fragment数据
                        try {
                            Object weakRef = param.args[0];
                            if (weakRef != null) {
                                Object fragment = XposedHelpers.callMethod(weakRef, "get");
                                if (fragment != null) {
                                    XposedBridge.log(TAG + ": 从SkuDataProvider获取到Fragment: " + 
                                        fragment.getClass().getSimpleName());
                                    extractFragmentGoodsData(fragment);
                                }
                            }
                        } catch (Exception e) {
                            XposedBridge.log(TAG + ": 从SkuDataProvider提取数据失败: " + e.getMessage());
                        }
                    }
                }
            );
            
            XposedBridge.log(TAG + ": 成功Hook GoodsDetailSkuDataProvider");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook GoodsDetailSkuDataProvider失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook商品详情API请求
     */
    private void hookGoodsDetailAPI(LoadPackageParam lpparam) {
        try {
            // Hook OkHttp Response
            Class<?> responseClass = XposedHelpers.findClass("okhttp3.Response", lpparam.classLoader);
            XposedHelpers.findAndHookMethod(responseClass, "body", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        Object response = param.thisObject;
                        Object request = XposedHelpers.callMethod(response, "request");
                        Object url = XposedHelpers.callMethod(request, "url");
                        String urlString = url.toString();
                        
                        // 检查是否是商品详情API
                        if (isGoodsDetailAPI(urlString)) {
                            XposedBridge.log(TAG + ": ✅ 捕获商品详情API: " + urlString);
                            
                            // 读取响应内容
                            Object responseBody = param.getResult();
                            if (responseBody != null) {
                                try {
                                    String content = XposedHelpers.callMethod(responseBody, "string").toString();
                                    if (content.length() > 100) {
                                        XposedBridge.log(TAG + ": 🎯 商品详情API响应: " + 
                                            (content.length() > 1000 ? content.substring(0, 1000) + "..." : content));
                                    }
                                } catch (Exception e) {
                                    XposedBridge.log(TAG + ": 读取API响应失败: " + e.getMessage());
                                }
                            }
                        }
                    } catch (Exception e) {
                        // 忽略网络Hook中的异常
                    }
                }
            });
            
            XposedBridge.log(TAG + ": 成功Hook商品详情API");
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook商品详情API失败: " + e.getMessage());
        }
    }
    
    /**
     * 判断URL是否是商品详情API
     */
    private boolean isGoodsDetailAPI(String url) {
        if (url == null) return false;
        String lowerUrl = url.toLowerCase();
        return (lowerUrl.contains("goods") && lowerUrl.contains("detail")) ||
               (lowerUrl.contains("product") && lowerUrl.contains("info")) ||
               lowerUrl.contains("goodsdetail") ||
               lowerUrl.contains("item/detail") ||
               (lowerUrl.contains("api") && lowerUrl.contains("goods"));
    }
    
    /**
     * 从Fragment中提取商品数据 - 使用配置管理器
     */
    private void extractFragmentGoodsData(Object fragment) {
        if (fragment == null) return;

        try {
            if (configManager.isLoggingEnabled()) {
                XposedBridge.log(TAG + ": 🔍 开始深度提取Fragment商品数据");
            }

            // 从配置获取关键字段
            List<String> criticalFields = configManager.getCriticalFields(MODULE_NAME);
            List<String> importantKeywords = configManager.getImportantKeywords(MODULE_NAME);

            Field[] fields = fragment.getClass().getDeclaredFields();
            int foundCount = 0;
            int maxFieldCount = configManager.getMaxFieldCount();

            for (Field field : fields) {
                if (foundCount >= maxFieldCount) {
                    if (configManager.isLoggingEnabled()) {
                        XposedBridge.log(TAG + ": 📊 已达到最大字段数限制: " + maxFieldCount);
                    }
                    break;
                }

                try {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    Object value = field.get(fragment);

                    if (value != null) {
                        // 检查关键字段
                        if (criticalFields.contains(fieldName)) {
                            if (configManager.isLoggingEnabled()) {
                                XposedBridge.log(TAG + ": 🎯 关键字段 " + fieldName + ": " + getValueDescription(value));
                            }
                            foundCount++;

                            if (isGoodsRelatedObject(value)) {
                                logDetailedGoodsData(value, "Fragment关键字段: " + fieldName);
                            }
                        }
                        // 检查重要关键词
                        else if (containsImportantKeyword(fieldName, importantKeywords)) {
                            if (configManager.isLoggingEnabled()) {
                                XposedBridge.log(TAG + ": 🔑 重要字段 " + fieldName + ": " + getValueDescription(value));
                            }
                            foundCount++;

                            if (isGoodsRelatedObject(value)) {
                                logDetailedGoodsData(value, "Fragment重要字段: " + fieldName);
                            }
                        }
                        // 检查商品相关对象
                        else if (isGoodsRelatedObject(value)) {
                            if (configManager.isLoggingEnabled()) {
                                XposedBridge.log(TAG + ": 🛍️ 商品相关对象 " + fieldName + ": " + value.getClass().getSimpleName());
                            }
                            logDetailedGoodsData(value, "Fragment商品对象: " + fieldName);
                            foundCount++;
                        }
                    }
                } catch (Exception e) {
                    // 忽略无法访问的字段
                }
            }

            if (configManager.isLoggingEnabled()) {
                XposedBridge.log(TAG + ": 📊 Fragment数据提取完成，发现 " + foundCount + " 个相关字段");
            }

        } catch (Exception e) {
            XposedBridge.log(TAG + ": Fragment数据提取失败: " + e.getMessage());
        }
    }

    /**
     * 检查字段名是否包含重要关键词
     */
    private boolean containsImportantKeyword(String fieldName, List<String> keywords) {
        String lowerFieldName = fieldName.toLowerCase();
        for (String keyword : keywords) {
            if (lowerFieldName.contains(keyword.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 详细记录商品数据 - 使用配置管理器
     */
    private void logDetailedGoodsData(Object goodsData, String source) {
        if (goodsData == null || !configManager.isLoggingEnabled()) return;

        try {
            StringBuilder sb = new StringBuilder();
            sb.append("\n🎯========== ").append(source).append(" ==========\n");
            sb.append("📦 对象类型: ").append(goodsData.getClass().getName()).append("\n");

            if (configManager.shouldIncludeTimestamp()) {
                sb.append("⏰ 时间: ").append(new java.util.Date()).append("\n");
            }
            sb.append("----------------------------------------\n");

            Field[] fields = goodsData.getClass().getDeclaredFields();
            int loggedCount = 0;
            int maxFieldCount = configManager.getMaxFieldCount();
            List<String> importantKeywords = configManager.getImportantKeywords(MODULE_NAME);

            for (Field field : fields) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(goodsData);

                    if (value != null && shouldLogField(field.getName(), value, importantKeywords)) {
                        String valueDesc = getValueDescription(value);
                        sb.append("  📋 ").append(field.getName()).append(": ").append(valueDesc).append("\n");
                        loggedCount++;

                        // 使用配置的最大字段数
                        if (loggedCount >= maxFieldCount) {
                            sb.append("  ... (更多字段已省略，最大显示: ").append(maxFieldCount).append(")\n");
                            break;
                        }
                    }
                } catch (Exception e) {
                    // 忽略无法访问的字段
                }
            }

            sb.append("----------------------------------------\n");
            sb.append("📊 记录字段数: ").append(loggedCount).append("/").append(fields.length).append("\n");
            sb.append("==========================================\n");

            String logMessage = sb.toString();
            int maxLogLength = configManager.getMaxLogLength();
            if (logMessage.length() > maxLogLength) {
                logMessage = logMessage.substring(0, maxLogLength) + "... (日志已截断)";
            }

            XposedBridge.log(TAG + ": " + logMessage);
            Log.d(TAG, logMessage);

        } catch (Exception e) {
            XposedBridge.log(TAG + ": 记录商品数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否应该记录字段 - 使用配置管理器
     */
    private boolean shouldLogField(String fieldName, Object value, List<String> keywords) {
        if (value == null) return false;

        String lowerFieldName = fieldName.toLowerCase();

        // 使用配置的关键词
        for (String keyword : keywords) {
            if (lowerFieldName.contains(keyword.toLowerCase())) {
                return true;
            }
        }

        // 基本数据类型的非默认值
        if (value instanceof String && !((String) value).isEmpty()) return true;
        if (value instanceof Number && !value.equals(0) && !value.equals(0L) && !value.equals(0.0)) return true;
        if (value instanceof Boolean) return true;

        return false;
    }

    /**
     * 获取值描述 - 使用配置管理器
     */
    private String getValueDescription(Object value) {
        if (value == null) return "null";

        String valueStr = value.toString();
        String className = value.getClass().getSimpleName();

        int maxLength = configManager.getValueMaxLength();
        if (valueStr.length() > maxLength) {
            valueStr = valueStr.substring(0, maxLength) + "...";
        }

        return className + "(" + valueStr + ")";
    }
    
    /**
     * 判断对象是否与商品相关
     */
    private boolean isGoodsRelatedObject(Object obj) {
        if (obj == null) return false;
        String className = obj.getClass().getName().toLowerCase();
        return className.contains("goods") || className.contains("product") || 
               className.contains("sku") || className.contains("entity") ||
               className.contains("response") || className.contains("item") ||
               className.contains("mall") || className.contains("detail");
    }
}
