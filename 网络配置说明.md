# 网络配置说明

## 📡 网络架构

```
手机(Xposed模块) ----WiFi----> 电脑(Python脚本)
    Android App              Windows/Linux/Mac
    IP: 自动获取              IP: 需要配置
    端口: 8888(内部)          端口: 9999(监听)
```

## 🔧 配置步骤

### 1. 获取电脑IP地址

#### Windows系统
```cmd
ipconfig
```
查找"无线局域网适配器 WLAN"或"以太网适配器"下的IPv4地址

#### Linux/Mac系统
```bash
ifconfig
# 或者
ip addr show
```

#### 示例输出
```
无线局域网适配器 WLAN:
   IPv4 地址 . . . . . . . . . . . . : **************
```

### 2. 修改Xposed模块配置

编辑文件：`pdd_nixinag/app/src/main/java/com/example/pdd_nixinag/NetworkConfig.java`

```java
public class NetworkConfig {
    // 修改这里的IP地址为你的电脑IP
    public static final String COMPUTER_IP = "**************";
    
    // 其他配置一般不需要修改
    public static final int PYTHON_MONITOR_PORT = 9999;
    public static final int XPOSED_SERVER_PORT = 8888;
}
```

### 3. 重新编译模块

```bash
cd pdd_nixinag
./gradlew assembleDebug
```

### 4. 安装到手机

1. 将生成的APK安装到手机
2. 在LSPosed中激活模块
3. 重启拼多多App

## 🌐 网络要求

### 必要条件
- 手机和电脑连接到同一个WiFi网络
- 电脑防火墙允许9999端口入站连接
- 网络路由器允许设备间通信

### 端口说明
- **9999**: Python脚本监听端口（电脑）
- **8888**: Xposed模块HTTP服务器端口（手机内部）

## 🔍 网络测试

### 1. 测试电脑Python服务器

启动Python脚本后，应该看到：
```
✅ HTTP服务器启动成功，监听端口: 9999
🌐 Python HTTP服务器监听: http://localhost:9999
```

### 2. 测试手机到电脑的连接

在手机浏览器中访问：
```
http://**************:9999/ping
```

如果配置正确，应该返回JSON响应。

### 3. 查看Xposed日志

在LSPosed日志中查找：
```
PDDJsonDataHook: 网络配置:
电脑IP: **************
Python端口: 9999
Xposed端口: 8888
Python URL: http://**************:9999/data
```

## 🛠️ 常见问题

### 问题1: 连接超时
**症状**: Xposed日志显示"发送HTTP请求异常: timeout"

**解决方案**:
1. 确认IP地址正确
2. 检查防火墙设置
3. 确认手机和电脑在同一网络

### 问题2: 连接被拒绝
**症状**: "Connection refused"

**解决方案**:
1. 确认Python脚本正在运行
2. 检查端口9999是否被占用
3. 重启Python脚本

### 问题3: 无法获取电脑IP
**解决方案**:
1. 使用路由器管理页面查看设备列表
2. 使用网络扫描工具
3. 临时关闭防火墙测试

## 🔒 防火墙配置

### Windows防火墙
1. 打开"Windows Defender 防火墙"
2. 点击"允许应用或功能通过Windows Defender防火墙"
3. 添加Python.exe到允许列表
4. 或者添加端口9999到入站规则

### 临时关闭防火墙测试
```cmd
# 管理员权限运行
netsh advfirewall set allprofiles state off
# 测试完成后重新开启
netsh advfirewall set allprofiles state on
```

## 📱 不同网络环境配置

### 家庭WiFi网络
- 通常IP格式: 192.168.1.x 或 192.168.0.x
- 路由器一般允许设备间通信

### 公司/学校网络
- 可能有设备隔离策略
- 需要网络管理员协助
- 考虑使用热点模式

### 手机热点模式
1. 手机开启热点
2. 电脑连接手机热点
3. 手机IP通常为: ************
4. 电脑IP通常为: 192.168.43.x

## 🚀 快速配置脚本

创建批处理文件 `config_network.bat`:

```batch
@echo off
echo 正在获取本机IP地址...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    set ip=%%a
    set ip=!ip: =!
    echo 检测到IP: !ip!
)
echo.
echo 请手动修改 NetworkConfig.java 中的 COMPUTER_IP 为上述IP地址
pause
```

## 📋 配置检查清单

- [ ] 获取电脑正确IP地址
- [ ] 修改NetworkConfig.java中的COMPUTER_IP
- [ ] 重新编译Xposed模块
- [ ] 安装APK到手机
- [ ] 在LSPosed中激活模块
- [ ] 启动Python监控脚本
- [ ] 测试网络连接
- [ ] 配置防火墙规则
- [ ] 重启拼多多App进行测试

## 💡 高级配置

### 动态IP检测
可以修改代码实现动态IP检测，但需要额外的网络权限和复杂度。

### 多IP支持
可以配置多个备用IP地址，提高连接成功率。

### 加密传输
可以添加HTTPS支持，但需要证书配置。

---

**注意**: 每次更改网络环境或IP地址时，都需要重新配置和编译模块。
