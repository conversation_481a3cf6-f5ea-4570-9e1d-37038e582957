2025-09-02 14:40:02,817 - INFO - 初始化监控器
2025-09-02 14:40:02,818 - INFO - Python监听端口: 9999
2025-09-02 14:40:02,818 - INFO - Xposed服务器: http://localhost:8888
2025-09-02 14:40:02,818 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:40:02,827 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:40:06,887 - ERROR - ❌ 无法连接到Xposed服务器: HTTPConnectionPool(host='localhost', port=8888): Max retries exceeded with url: /ping (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000215A0AB0440>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:40:06,887 - WARNING - ⚠️ 无法连接到Xposed模块HTTP服务器
2025-09-02 14:40:06,887 - INFO - 这不影响数据接收功能，但请确保:
2025-09-02 14:40:06,888 - INFO - 1. 拼多多App已启动
2025-09-02 14:40:06,888 - INFO - 2. Xposed模块已激活
2025-09-02 14:40:06,888 - INFO - 3. Xposed模块HTTP服务器正在运行
2025-09-02 14:40:06,889 - INFO - 开始监控循环
2025-09-02 14:40:06,889 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:40:06,889 - INFO - 🌐 Python HTTP服务器监听: http://localhost:9999
2025-09-02 14:40:06,889 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:40:06,890 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 14:40:09,796 - INFO - 收到停止信号
2025-09-02 14:40:09,796 - INFO - 🛑 停止监控
2025-09-02 14:40:09,926 - INFO - HTTP服务器已停止
2025-09-02 14:41:01,639 - INFO - 初始化监控器
2025-09-02 14:41:01,640 - INFO - Python监听端口: 9999
2025-09-02 14:41:01,640 - INFO - Xposed服务器: http://**************:8888
2025-09-02 14:41:01,640 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:41:01,647 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:41:01,785 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756795261642
2025-09-02 14:41:01,786 - INFO - 开始监控循环
2025-09-02 14:41:01,786 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:41:01,786 - INFO - 🌐 Python HTTP服务器监听: http://localhost:9999
2025-09-02 14:41:01,787 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:41:01,787 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 14:42:02,631 - INFO - 收到停止信号
2025-09-02 14:42:02,631 - INFO - 🛑 停止监控
2025-09-02 14:46:08,129 - INFO - 初始化监控器
2025-09-02 14:46:08,129 - INFO - Python监听端口: 9999
2025-09-02 14:46:08,129 - INFO - Xposed服务器: http://**************:8888
2025-09-02 14:46:08,129 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:46:08,136 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:46:10,597 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756795570466
2025-09-02 14:46:10,597 - INFO - 开始监控循环
2025-09-02 14:46:10,597 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:46:10,598 - INFO - 🌐 Python HTTP服务器监听: http://localhost:9999
2025-09-02 14:46:10,599 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:46:10,599 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 14:46:39,419 - INFO - 收到停止信号
2025-09-02 14:46:39,419 - INFO - 🛑 停止监控
2025-09-02 14:48:13,748 - INFO - 初始化监控器
2025-09-02 14:48:13,748 - INFO - Python监听端口: 9999
2025-09-02 14:48:13,748 - INFO - Xposed服务器: http://**************:8888
2025-09-02 14:48:13,749 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:48:13,755 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:48:13,927 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756795693767
2025-09-02 14:48:13,927 - INFO - 开始监控循环
2025-09-02 14:48:13,927 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:48:13,927 - INFO - 🌐 Python HTTP服务器监听: http://localhost:9999
2025-09-02 14:48:13,928 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:48:13,928 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 14:49:23,181 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD48F200>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:49:27,563 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD48F3E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:49:34,047 - INFO - 初始化监控器
2025-09-02 14:49:34,047 - INFO - Python监听端口: 9999
2025-09-02 14:49:34,047 - INFO - Xposed服务器: http://**************:8888
2025-09-02 14:49:34,047 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:49:34,055 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:49:34,207 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756795774052
2025-09-02 14:49:34,207 - INFO - 开始监控循环
2025-09-02 14:49:34,208 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:49:34,209 - INFO - 🌐 Python HTTP服务器监听: http://localhost:9999
2025-09-02 14:49:34,209 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:49:34,209 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 14:50:32,817 - INFO - 收到停止信号
2025-09-02 14:50:32,818 - INFO - 🛑 停止监控
2025-09-02 14:50:32,891 - INFO - HTTP服务器已停止
2025-09-02 14:50:34,430 - INFO - 初始化监控器
2025-09-02 14:50:34,430 - INFO - Python监听端口: 9999
2025-09-02 14:50:34,430 - INFO - Xposed服务器: http://**************:8888
2025-09-02 14:50:34,431 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:50:34,443 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:50:34,533 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756795834390
2025-09-02 14:50:34,535 - INFO - 开始监控循环
2025-09-02 14:50:34,535 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:50:34,536 - INFO - 🌐 Python HTTP服务器监听: http://localhost:9999
2025-09-02 14:50:34,537 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:50:34,538 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 14:50:47,047 - INFO - 收到停止信号
2025-09-02 14:50:47,047 - INFO - 🛑 停止监控
2025-09-02 14:50:47,072 - INFO - HTTP服务器已停止
2025-09-02 14:50:49,221 - INFO - 初始化监控器
2025-09-02 14:50:49,221 - INFO - Python监听端口: 9999
2025-09-02 14:50:49,221 - INFO - Xposed服务器: http://**************:8888
2025-09-02 14:50:49,222 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:50:49,228 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:50:49,343 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756795849207
2025-09-02 14:50:49,344 - INFO - 开始监控循环
2025-09-02 14:50:49,344 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:50:49,344 - INFO - 🌐 Python HTTP服务器监听: http://**************:9999
2025-09-02 14:50:49,344 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:50:49,345 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 14:51:49,273 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD544050>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:51:49,273 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002044FFAF200>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:51:53,675 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD5446E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:51:53,676 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002044FFAF3E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:52:44,729 - INFO - 收到停止信号
2025-09-02 14:52:44,730 - INFO - 🛑 停止监控
2025-09-02 14:58:53,437 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD544D70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:58:57,834 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD545400>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:59:02,239 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD545A90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:59:06,656 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD546120>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:59:10,751 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD5467B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:59:11,811 - INFO - 初始化监控器
2025-09-02 14:59:11,811 - INFO - Python监听端口: 9999
2025-09-02 14:59:11,811 - INFO - Xposed服务器: http://**************:8888
2025-09-02 14:59:11,811 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 14:59:11,819 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 14:59:13,896 - ERROR - ❌ 无法连接到Xposed服务器: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /ping (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016F423B0440>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 14:59:13,897 - WARNING - ⚠️ 无法连接到Xposed模块HTTP服务器
2025-09-02 14:59:13,907 - INFO - 这不影响数据接收功能，但请确保:
2025-09-02 14:59:13,907 - INFO - 1. 拼多多App已启动
2025-09-02 14:59:13,907 - INFO - 2. Xposed模块已激活
2025-09-02 14:59:13,907 - INFO - 3. Xposed模块HTTP服务器正在运行
2025-09-02 14:59:13,908 - INFO - 开始监控循环
2025-09-02 14:59:13,908 - INFO - 📱 监控已启动，等待数据...
2025-09-02 14:59:13,909 - INFO - 🌐 Python HTTP服务器监听: http://**************:9999
2025-09-02 14:59:13,909 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 14:59:13,910 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 15:00:37,759 - INFO - 收到停止信号
2025-09-02 15:00:37,759 - INFO - 🛑 停止监控
2025-09-02 15:00:37,919 - INFO - HTTP服务器已停止
2025-09-02 15:00:58,705 - INFO - 初始化监控器
2025-09-02 15:00:58,711 - INFO - Python监听端口: 9999
2025-09-02 15:00:58,711 - INFO - Xposed服务器: http://**************:8888
2025-09-02 15:00:58,712 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 15:00:58,719 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 15:00:58,856 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756796458724
2025-09-02 15:00:58,860 - INFO - 开始监控循环
2025-09-02 15:00:58,860 - INFO - 📱 监控已启动，等待数据...
2025-09-02 15:00:58,863 - INFO - 🌐 Python HTTP服务器监听: http://**************:9999
2025-09-02 15:00:58,863 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 15:00:58,864 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 15:01:07,135 - INFO - 初始化监控器
2025-09-02 15:01:07,135 - INFO - Python监听端口: 9999
2025-09-02 15:01:07,136 - INFO - Xposed服务器: http://**************:8888
2025-09-02 15:01:07,136 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 15:01:07,145 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 15:01:07,288 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756796467150
2025-09-02 15:01:07,289 - INFO - 开始监控循环
2025-09-02 15:01:07,289 - INFO - 📱 监控已启动，等待数据...
2025-09-02 15:01:07,289 - INFO - 🌐 Python HTTP服务器监听: http://**************:9999
2025-09-02 15:01:07,289 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 15:01:07,290 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 15:01:29,386 - INFO - 收到停止信号
2025-09-02 15:01:29,386 - INFO - 🛑 停止监控
2025-09-02 15:01:29,516 - INFO - HTTP服务器已停止
2025-09-02 15:01:33,037 - INFO - 初始化监控器
2025-09-02 15:01:33,037 - INFO - Python监听端口: 9999
2025-09-02 15:01:33,037 - INFO - Xposed服务器: http://**************:8888
2025-09-02 15:01:33,038 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 15:01:33,044 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 15:01:33,203 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756796493068
2025-09-02 15:01:33,208 - INFO - 开始监控循环
2025-09-02 15:01:33,208 - INFO - 📱 监控已启动，等待数据...
2025-09-02 15:01:33,209 - INFO - 🌐 Python HTTP服务器监听: http://**************:9999
2025-09-02 15:01:33,209 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 15:01:33,210 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 15:05:17,693 - INFO - 收到停止信号
2025-09-02 15:05:17,693 - INFO - 🛑 停止监控
2025-09-02 15:05:17,849 - INFO - HTTP服务器已停止
2025-09-02 15:05:21,644 - INFO - 初始化监控器
2025-09-02 15:05:21,645 - INFO - Python监听端口: 9999
2025-09-02 15:05:21,645 - INFO - Xposed服务器: http://**************:8888
2025-09-02 15:05:21,645 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 15:05:21,653 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 15:05:21,740 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756796721608
2025-09-02 15:05:21,745 - INFO - 开始监控循环
2025-09-02 15:05:21,746 - INFO - 📱 监控已启动，等待数据...
2025-09-02 15:05:21,747 - INFO - 🌐 Python HTTP服务器监听: http://**************:9999
2025-09-02 15:05:21,747 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 15:05:21,748 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 15:05:44,184 - INFO - 收到停止信号
2025-09-02 15:05:44,222 - INFO - 🛑 停止监控
2025-09-02 15:05:44,464 - INFO - HTTP服务器已停止
2025-09-02 15:06:22,761 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD546E40>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:06:22,761 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B3604A3110>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:06:27,045 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B3604A32F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:06:27,056 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000242DD48F2F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:06:47,864 - INFO - 收到停止信号
2025-09-02 15:06:47,864 - INFO - 🛑 停止监控
2025-09-02 15:06:47,974 - INFO - HTTP服务器已停止
2025-09-02 15:06:48,133 - INFO - 收到停止信号
2025-09-02 15:06:48,134 - INFO - 🛑 停止监控
2025-09-02 15:06:48,399 - INFO - HTTP服务器已停止
2025-09-02 15:06:52,568 - INFO - 初始化监控器
2025-09-02 15:06:52,569 - INFO - Python监听端口: 9999
2025-09-02 15:06:52,569 - INFO - Xposed服务器: http://**************:8888
2025-09-02 15:06:52,569 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 15:06:52,579 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 15:06:52,670 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756796812538
2025-09-02 15:06:52,671 - INFO - 开始监控循环
2025-09-02 15:06:52,671 - INFO - 📱 监控已启动，等待数据...
2025-09-02 15:06:52,672 - INFO - 🌐 Python HTTP服务器监听: http://**************:9999
2025-09-02 15:06:52,676 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 15:06:52,676 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 15:07:13,468 - INFO - 收到停止信号
2025-09-02 15:07:13,468 - INFO - 🛑 停止监控
2025-09-02 15:11:21,034 - INFO - 初始化监控器
2025-09-02 15:11:21,035 - INFO - Python监听端口: 9999
2025-09-02 15:11:21,035 - INFO - Xposed服务器: http://**************:8888
2025-09-02 15:11:21,035 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 15:11:21,041 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 15:11:21,154 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756797081011
2025-09-02 15:11:21,155 - INFO - 开始监控循环
2025-09-02 15:11:21,155 - INFO - 📱 监控已启动，等待数据...
2025-09-02 15:11:21,156 - INFO - 🌐 Python HTTP服务器监听: http://**************:9999
2025-09-02 15:11:21,157 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 15:11:21,158 - INFO - ⌨️  按 Ctrl+C 停止监控
2025-09-02 15:13:54,037 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000196C89F3110>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:13:58,471 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000196C89F3200>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:14:02,730 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000196C89F3F20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:14:07,053 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000196C8AAC5F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:14:11,559 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000196C8AACC80>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:14:15,643 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000196C8AAD310>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:14:19,732 - ERROR - 获取Xposed服务器状态失败: HTTPConnectionPool(host='**************', port=8888): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000196C8AAD9A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-09-02 15:14:29,380 - INFO - 收到停止信号
2025-09-02 15:14:29,381 - INFO - 🛑 停止监控
2025-09-02 15:14:29,536 - INFO - HTTP服务器已停止
2025-09-02 15:14:31,682 - INFO - 初始化监控器
2025-09-02 15:14:31,683 - INFO - Python监听端口: 9999
2025-09-02 15:14:31,683 - INFO - Xposed服务器: http://**************:8888
2025-09-02 15:14:31,683 - INFO - 🚀 启动拼多多数据监控器
2025-09-02 15:14:31,689 - INFO - ✅ HTTP服务器启动成功，监听端口: 9999
2025-09-02 15:14:31,820 - INFO - ✅ Xposed服务器在线 - 端口: 8888, 时间: 1756797271663
2025-09-02 15:14:31,821 - INFO - 开始监控循环
2025-09-02 15:14:31,821 - INFO - 📱 监控已启动，等待数据...
2025-09-02 15:14:31,821 - INFO - 🌐 Python HTTP服务器监听: http://**************:9999
2025-09-02 15:14:31,821 - INFO - 💡 提示: 在拼多多App中浏览商品以触发数据捕获
2025-09-02 15:14:31,822 - INFO - ⌨️  按 Ctrl+C 停止监控
