package com.example.pdd_nixinag;

import android.util.Log;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import de.robv.android.xposed.XposedBridge;
import fi.iki.elonen.NanoHTTPD;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.ServerSocket;
import java.net.URL;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * HTTP服务器，用于接收Xposed模块的数据并转发给Python脚本
 */
public class PDDHttpServer extends NanoHTTPD {
    
    private static final String TAG = "PDDHttpServer";
    private static final int DEFAULT_PORT = 8888;
    private static final int MAX_PORT_ATTEMPTS = 100;
    
    private static PDDHttpServer instance;
    private static final AtomicBoolean isRunning = new AtomicBoolean(false);
    private static final AtomicInteger dataCounter = new AtomicInteger(0);
    
    private final Gson gson = new Gson();
    private final ConcurrentHashMap<String, Long> dataDeduplication = new ConcurrentHashMap<>();
    
    private PDDHttpServer(int port) throws IOException {
        super(port);
        XposedBridge.log(TAG + ": HTTP服务器初始化，端口: " + port);
    }
    
    /**
     * 获取单例实例，确保只有一个服务器运行
     */
    public static synchronized PDDHttpServer getInstance() {
        if (instance == null && !isRunning.get()) {
            try {
                int availablePort = findAvailablePort();
                instance = new PDDHttpServer(availablePort);
                instance.start(NanoHTTPD.SOCKET_READ_TIMEOUT, false);
                isRunning.set(true);
                XposedBridge.log(TAG + ": ✅ HTTP服务器启动成功，端口: " + availablePort);
                Log.i(TAG, "HTTP服务器启动成功，端口: " + availablePort);
            } catch (IOException e) {
                XposedBridge.log(TAG + ": ❌ HTTP服务器启动失败: " + e.getMessage());
                Log.e(TAG, "HTTP服务器启动失败", e);
            }
        }
        return instance;
    }
    
    /**
     * 查找可用端口
     */
    private static int findAvailablePort() {
        for (int port = DEFAULT_PORT; port < DEFAULT_PORT + MAX_PORT_ATTEMPTS; port++) {
            try (ServerSocket socket = new ServerSocket(port)) {
                return port;
            } catch (IOException e) {
                // 端口被占用，尝试下一个
            }
        }
        throw new RuntimeException("无法找到可用端口");
    }
    
    @Override
    public Response serve(IHTTPSession session) {
        String uri = session.getUri();
        Method method = session.getMethod();
        
        XposedBridge.log(TAG + ": 收到请求 " + method + " " + uri);
        
        try {
            if ("/ping".equals(uri)) {
                return handlePing();
            } else if ("/data".equals(uri) && Method.POST.equals(method)) {
                return handleDataReceive(session);
            } else if ("/status".equals(uri)) {
                return handleStatus();
            } else {
                return newFixedLengthResponse(Response.Status.NOT_FOUND, 
                    MIME_PLAINTEXT, "404 Not Found");
            }
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 处理请求异常: " + e.getMessage());
            return newFixedLengthResponse(Response.Status.INTERNAL_ERROR, 
                MIME_PLAINTEXT, "Internal Server Error: " + e.getMessage());
        }
    }
    
    /**
     * 处理ping请求
     */
    private Response handlePing() {
        JsonObject response = new JsonObject();
        response.addProperty("status", "ok");
        response.addProperty("server", "PDDHttpServer");
        response.addProperty("timestamp", System.currentTimeMillis());
        response.addProperty("port", getListeningPort());
        
        return newFixedLengthResponse(Response.Status.OK, 
            "application/json", gson.toJson(response));
    }
    
    /**
     * 处理数据接收请求
     */
    private Response handleDataReceive(IHTTPSession session) {
        try {
            // 读取POST数据
            String postData = getPostData(session);
            if (postData == null || postData.trim().isEmpty()) {
                return newFixedLengthResponse(Response.Status.BAD_REQUEST, 
                    MIME_PLAINTEXT, "Empty data");
            }
            
            // 数据去重
            String dataHash = String.valueOf(postData.hashCode());
            long currentTime = System.currentTimeMillis();
            Long lastTime = dataDeduplication.get(dataHash);
            
            if (lastTime != null && (currentTime - lastTime) < 1000) {
                // 1秒内的重复数据忽略
                return newFixedLengthResponse(Response.Status.OK, 
                    MIME_PLAINTEXT, "Duplicate data ignored");
            }
            
            dataDeduplication.put(dataHash, currentTime);
            
            // 清理过期的去重记录
            cleanupDeduplication(currentTime);
            
            // 处理数据
            processReceivedData(postData);
            
            JsonObject response = new JsonObject();
            response.addProperty("status", "success");
            response.addProperty("message", "Data received");
            response.addProperty("counter", dataCounter.incrementAndGet());
            response.addProperty("timestamp", currentTime);
            
            return newFixedLengthResponse(Response.Status.OK, 
                "application/json", gson.toJson(response));
                
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 处理数据异常: " + e.getMessage());
            return newFixedLengthResponse(Response.Status.INTERNAL_ERROR, 
                MIME_PLAINTEXT, "Error processing data: " + e.getMessage());
        }
    }
    
    /**
     * 处理状态查询
     */
    private Response handleStatus() {
        JsonObject status = new JsonObject();
        status.addProperty("running", isRunning.get());
        status.addProperty("port", getListeningPort());
        status.addProperty("dataCount", dataCounter.get());
        status.addProperty("deduplicationSize", dataDeduplication.size());
        status.addProperty("timestamp", System.currentTimeMillis());
        
        return newFixedLengthResponse(Response.Status.OK, 
            "application/json", gson.toJson(status));
    }
    
    /**
     * 获取POST数据
     */
    private String getPostData(IHTTPSession session) {
        try {
            int contentLength = Integer.parseInt(
                session.getHeaders().get("content-length"));
            
            if (contentLength > 0) {
                byte[] buffer = new byte[contentLength];
                session.getInputStream().read(buffer, 0, contentLength);
                return new String(buffer, "UTF-8");
            }
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 读取POST数据失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 处理接收到的数据
     */
    private void processReceivedData(String jsonData) {
        try {
            // 记录接收到的数据
            XposedBridge.log(TAG + ": 📨 接收到数据: " + jsonData);
            Log.i(TAG, "接收到数据: " + jsonData);
            
            // 这里可以添加更多的数据处理逻辑
            // 比如数据验证、格式化、存储等
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 处理数据异常: " + e.getMessage());
        }
    }
    
    /**
     * 清理过期的去重记录
     */
    private void cleanupDeduplication(long currentTime) {
        if (dataDeduplication.size() > 1000) {
            dataDeduplication.entrySet().removeIf(entry -> 
                (currentTime - entry.getValue()) > 60000); // 清理1分钟前的记录
        }
    }
    
    /**
     * 发送数据到指定URL
     */
    public void sendDataToUrl(String urlString, String jsonData) {
        new Thread(() -> {
            try {
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();

                // 设置请求方法和属性
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
                connection.setRequestProperty("User-Agent", "PDDHttpServer/1.0");
                connection.setDoOutput(true);
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(10000);

                // 发送数据
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = jsonData.getBytes("utf-8");
                    os.write(input, 0, input.length);
                }

                // 获取响应
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    XposedBridge.log(TAG + ": 数据发送成功，响应码: " + responseCode);
                } else {
                    XposedBridge.log(TAG + ": 数据发送失败，响应码: " + responseCode);
                }

                connection.disconnect();

            } catch (Exception e) {
                XposedBridge.log(TAG + ": 发送数据异常: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * 停止服务器
     */
    public static synchronized void stopServer() {
        if (instance != null && isRunning.get()) {
            instance.stop();
            instance = null;
            isRunning.set(false);
            XposedBridge.log(TAG + ": HTTP服务器已停止");
        }
    }
    
    /**
     * 获取服务器状态
     */
    public static boolean isServerRunning() {
        return isRunning.get() && instance != null;
    }
    
    /**
     * 获取服务器端口
     */
    public static int getServerPort() {
        return instance != null ? instance.getListeningPort() : -1;
    }
}
