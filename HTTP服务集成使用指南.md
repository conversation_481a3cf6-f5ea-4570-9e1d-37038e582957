# 拼多多Xposed模块HTTP服务集成使用指南

## 📋 功能概述

本项目实现了一个完整的数据监控系统：
- **Xposed模块**: 捕获拼多多App的JSON数据，启动HTTP服务器，并将数据发送给Python脚本
- **Python监控脚本**: 实时接收和显示Xposed模块发送的数据

## 🏗️ 系统架构

```
拼多多App → Xposed模块 → HTTP请求 → Python监控脚本
    ↓           ↓                        ↓
  JSON数据   数据处理+HTTP服务器      实时显示+分析
```

## 🔧 技术实现

### Xposed模块端
- **HTTP服务器**: 使用NanoHTTPD在唯一端口启动服务器
- **数据捕获**: Hook JSON解析方法（Gson、JSONObject、FastJson等）
- **数据发送**: 使用OkHttp将数据发送到Python脚本
- **端口管理**: 自动查找可用端口，避免多次启动冲突

### Python脚本端
- **HTTP服务器**: 监听9999端口接收数据
- **数据处理**: 实时解析和显示JSON数据
- **去重机制**: 避免重复数据显示
- **统计功能**: 提供数据统计和分析

## 📦 安装步骤

### 1. 编译Xposed模块

```bash
cd pdd_nixinag
./gradlew assembleDebug
```

编译成功后，APK文件位于：`app/build/outputs/apk/debug/app-debug.apk`

### 2. 安装和激活模块

1. 将APK安装到手机
2. 在LSPosed中激活模块
3. 选择拼多多App作为作用域
4. 重启拼多多App

### 3. 安装Python依赖

```bash
pip install requests
```

## 🚀 使用方法

### 1. 启动Python监控脚本

```bash
python pdd_data_monitor.py
```

脚本启动后会显示：
```
🎯 拼多多数据监控器 v1.0
==================================================
🚀 启动拼多多数据监控器
✅ HTTP服务器启动成功，监听端口: 9999
📱 监控已启动，等待数据...
🌐 Python HTTP服务器监听: http://localhost:9999
💡 提示: 在拼多多App中浏览商品以触发数据捕获
⌨️  按 Ctrl+C 停止监控
```

### 2. 使用拼多多App

1. 确保Xposed模块已激活
2. 打开拼多多App
3. 浏览商品页面、搜索商品等操作
4. 观察Python脚本的输出

### 3. 数据输出示例

当捕获到数据时，Python脚本会显示：

```
================================================================================
🎯 数据接收 #1
⏰ 时间: 2025-01-02 14:10:13.341
🔧 解析类型: JSONObject
📦 数据类型: JSONObject
🔑 关键信息:
   商品ID: 804459157964
   图片: https://img.pddpic.com/mms-material-img/2025-05-16/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg
   page_from: 35
📄 完整JSON数据:
{
  "goods_id": "804459157964",
  "_oak_rcto": "YWJPpsgZllZP4JqbqBz9DJwtUg0tekZ7Yb3f9sz4gxd1-hmgISkTxiPM",
  "_oak_gallery_token": "64038bdf69bb7127251f2f0c7a66a657",
  "_oak_gallery": "https://img.pddpic.com/mms-material-img/2025-05-16/9d7d5971-482e-4c27-a183-8c1be506325e.jpeg.a.jpeg",
  "pr_force_native": "1"
}
================================================================================
```

## 🔍 监控的数据类型

系统会捕获以下类型的JSON数据：

### 商品信息
- 商品ID (`goods_id`)
- 商品名称 (`goods_name`)
- 商品价格 (`price`, `single_price`, `market_price`)
- 商品图片 (`thumb_url`, `_oak_gallery`)
- 商品描述和规格

### 页面信息
- 页面类型 (`page_type`)
- 页面URL (`page_url`)
- 页面来源 (`page_from`)
- 页面ID和时间戳

### 用户行为
- 浏览记录
- 搜索关键词
- 点击事件
- 页面跳转

### 商店信息
- 商店ID (`mall_id`)
- 商店名称
- 商店评级

## 📊 统计功能

Python脚本会定期显示统计信息：

```
📊 统计信息 ============================================================
运行时间: 0:05:23.123456
总接收数据: 45
最后数据时间: 2025-01-02 14:15:36.789
按解析类型统计:
  JSONObject: 32
  Gson: 13
按数据类型统计:
  JSONObject: 32
  com.xunmeng.pinduoduo.goods.entity.IntegrationRenderResponse: 8
  PageStack: 5
======================================================================
```

## 🛠️ 配置选项

### Python脚本配置

可以在`pdd_data_monitor.py`中修改以下参数：

```python
# 修改监听端口
monitor = PDDDataMonitor(listen_port=9999)

# 修改Xposed服务器地址
monitor = PDDDataMonitor(xposed_host='localhost', xposed_port=8888)

# 修改检查间隔
monitor = PDDDataMonitor(check_interval=2)
```

### Xposed模块配置

在`PDDJsonDataHook.java`中可以修改：

```java
// Python脚本地址
sendHttpRequest("http://localhost:9999/data", packetJson);

// 数据过滤条件
private boolean isGoodsRelatedJson(String json, Object typeInfo)
```

## 🔧 故障排除

### 1. 编译错误

如果遇到编译错误：
```bash
./gradlew clean
./gradlew assembleDebug
```

### 2. 模块未生效

- 确认LSPosed中模块已激活
- 确认作用域包含拼多多App
- 重启拼多多App
- 查看LSPosed日志

### 3. Python脚本无法接收数据

- 确认端口9999未被占用
- 确认防火墙设置
- 检查Xposed模块日志
- 确认拼多多App正在运行

### 4. 数据重复或缺失

- Python脚本有去重机制
- 可以调整去重时间窗口
- 检查网络连接状态

## 📝 日志文件

### Xposed模块日志
- 位置: LSPosed日志或logcat
- 标签: `PDDJsonDataHook`, `PDDHttpServer`

### Python脚本日志
- 控制台输出
- 文件: `pdd_monitor.log`

## 🔒 注意事项

1. **仅用于学习研究**: 本工具仅供技术学习和研究使用
2. **遵守法律法规**: 使用时请遵守相关法律法规
3. **数据安全**: 注意保护个人隐私和数据安全
4. **版本兼容**: 不同版本的拼多多App可能需要调整Hook点

## 📈 扩展功能

### 数据存储
可以修改Python脚本，将数据保存到：
- JSON文件
- CSV文件
- 数据库（SQLite、MySQL等）

### 数据分析
可以添加：
- 商品价格趋势分析
- 用户行为分析
- 数据可视化图表

### 远程监控
可以实现：
- Web界面显示
- 远程API接口
- 实时推送通知

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目仅供学习研究使用，请勿用于商业用途。
