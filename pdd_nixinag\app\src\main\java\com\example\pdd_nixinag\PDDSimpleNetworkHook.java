package com.example.pdd_nixinag;

import android.util.Log;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage.LoadPackageParam;

/**
 * 简化的网络Hook模块 - 专注于稳定性，避免闪退
 */
public class PDDSimpleNetworkHook implements IXposedHookLoadPackage {
    
    private static final String TAG = "PDDSimpleNetworkHook";
    private static final String TARGET_PACKAGE = "com.xunmeng.pinduoduo";
    
    @Override
    public void handleLoadPackage(LoadPackageParam lpparam) throws Throwable {
        if (!TARGET_PACKAGE.equals(lpparam.packageName)) {
            return;
        }
        
        LogUtils.i("🚀 启动简化网络Hook模块");
        
        // 只Hook最基础的网络方法，避免复杂的Hook导致闪退
        hookBasicNetworkMethods(lpparam);
        
        // Hook JSON解析，这个相对安全
        hookBasicJsonMethods(lpparam);
        
        LogUtils.i("✅ 简化网络Hook模块初始化完成");
    }
    
    /**
     * Hook基础网络方法
     */
    private void hookBasicNetworkMethods(LoadPackageParam lpparam) {
        // Hook HttpURLConnection - 这个比较安全
        try {
            Class<?> httpURLConnectionClass = XposedHelpers.findClass("java.net.HttpURLConnection", lpparam.classLoader);
            
            XposedHelpers.findAndHookMethod(httpURLConnectionClass, "getResponseCode", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        int responseCode = (Integer) param.getResult();
                        Object url = XposedHelpers.callMethod(param.thisObject, "getURL");
                        LogUtils.i("🌐 HTTP响应: [" + responseCode + "] " + url.toString());
                    } catch (Exception e) {
                        // 忽略异常
                    }
                }
            });
            
            LogUtils.i("✅ 成功Hook HttpURLConnection");
        } catch (Exception e) {
            LogUtils.w("Hook HttpURLConnection失败: " + e.getMessage());
        }
        
        // Hook URL类 - 捕获所有URL创建
        try {
            Class<?> urlClass = XposedHelpers.findClass("java.net.URL", lpparam.classLoader);
            
            XposedHelpers.findAndHookConstructor(urlClass, String.class, new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        String url = (String) param.args[0];
                        if (url != null && (url.contains("pinduoduo") || url.contains("pdd"))) {
                            LogUtils.i("🔗 URL创建: " + url);
                        }
                    } catch (Exception e) {
                        // 忽略异常
                    }
                }
            });
            
            LogUtils.i("✅ 成功Hook URL构造");
        } catch (Exception e) {
            LogUtils.w("Hook URL构造失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook基础JSON方法
     */
    private void hookBasicJsonMethods(LoadPackageParam lpparam) {
        // Hook JSONObject - Android原生JSON解析
        try {
            Class<?> jsonObjectClass = XposedHelpers.findClass("org.json.JSONObject", lpparam.classLoader);
            
            XposedHelpers.findAndHookConstructor(jsonObjectClass, String.class, new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        String jsonString = (String) param.args[0];
                        if (jsonString != null && jsonString.length() > 50 && 
                            (jsonString.contains("goods") || jsonString.contains("product") || 
                             jsonString.contains("price") || jsonString.contains("category"))) {
                            LogUtils.i("📄 JSON解析: " + (jsonString.length() > 500 ? 
                                jsonString.substring(0, 500) + "..." : jsonString));
                        }
                    } catch (Exception e) {
                        // 忽略异常
                    }
                }
            });
            
            LogUtils.i("✅ 成功Hook JSONObject");
        } catch (Exception e) {
            LogUtils.w("Hook JSONObject失败: " + e.getMessage());
        }
        
        // Hook String.contains - 捕获包含关键词的字符串操作
        try {
            Class<?> stringClass = String.class;
            
            XposedHelpers.findAndHookMethod(stringClass, "contains", CharSequence.class, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        String thisString = (String) param.thisObject;
                        String searchString = param.args[0].toString();
                        
                        // 只记录包含网络相关关键词的字符串操作
                        if (thisString != null && thisString.length() > 20 && thisString.length() < 1000) {
                            if ((thisString.contains("http") || thisString.contains("api") || thisString.contains("json")) &&
                                (searchString.equals("goods") || searchString.equals("product") || 
                                 searchString.equals("price") || searchString.equals("category"))) {
                                LogUtils.i("🔍 字符串搜索: 在 '" + 
                                    (thisString.length() > 200 ? thisString.substring(0, 200) + "..." : thisString) + 
                                    "' 中搜索 '" + searchString + "'");
                            }
                        }
                    } catch (Exception e) {
                        // 忽略异常
                    }
                }
            });
            
            LogUtils.i("✅ 成功Hook String.contains");
        } catch (Exception e) {
            LogUtils.w("Hook String.contains失败: " + e.getMessage());
        }
    }
}
