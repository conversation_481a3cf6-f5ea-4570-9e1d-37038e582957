package com.example.pdd_nixinag;

/**
 * 拼多多Hook目标配置类
 * 包含需要Hook的具体类名和方法名
 */
public class PDDHookTargets {
    
    // 商品相关类名
    public static final class GoodsClasses {
        public static final String GOODS_DETAIL_GALLERY_ACTIVITY = "com.xunmeng.pinduoduo.goods.gallery.GoodsDetailGalleryActivity";
        public static final String GOODS_ENTITY = "com.xunmeng.pinduoduo.goods.entity.GoodsEntity";
        public static final String GOODS_PHOTO_BROWSE_FRAGMENT = "com.xunmeng.pinduoduo.goods.browser.view.GoodsPhotoBrowseFragment";
        public static final String GOODS_DETAIL_SKU_DATA_PROVIDER = "com.xunmeng.pinduoduo.goods.model.GoodsDetailSkuDataProvider";
        public static final String CHECKOUT_PRODUCT_VIEW = "com.xunmeng.pinduoduo.sku_checkout.checkout.components.product.CheckoutProductView";
        
        // 商品实体相关
        public static final String GOODS_ENTITY_F1 = "com.xunmeng.pinduoduo.goods.entity.C9249f1";
        public static final String GOODS_ENTITY_A1 = "com.xunmeng.pinduoduo.goods.entity.C9234a1";
        public static final String GOODS_ENTITY_J0 = "com.xunmeng.pinduoduo.goods.entity.C9260j0";
        public static final String POSTCARD_EXT = "com.xunmeng.pinduoduo.goods.entity.PostcardExt";
    }
    
    // 搜索相关类名
    public static final class SearchClasses {
        public static final String SEARCH_RESULT_GOODS_NEW_FRAGMENT = "com.xunmeng.pinduoduo.search.fragment.SearchResultGoodsNewFragment";
        public static final String SEARCH_MALL_RESULT_NEW_FRAGMENT = "com.xunmeng.pinduoduo.search.fragment.SearchMallResultNewFragment";
    }
    
    // 商城相关类名
    public static final class MallClasses {
        public static final String MALL_FRAGMENT = "com.xunmeng.pinduoduo.mall.MallFragment";
        public static final String MALL_COMMENT_LIST_BROWSER_FRAGMENT = "com.xunmeng.pinduoduo.mall.comment.MallCommentListBrowserFragment";
    }
    
    // 订单相关类名
    public static final class OrderClasses {
        public static final String ORDER_HOLDER = "com.xunmeng.pinduoduo.order.holder.OrderHolder";
        public static final String ORDER_SEARCH_FRAGMENT = "com.xunmeng.pinduoduo.order.OrderSearchFragment";
    }
    
    // 网络请求相关类名
    public static final class NetworkClasses {
        public static final String OKHTTP3_REQUEST = "okhttp3.Request";
        public static final String OKHTTP3_RESPONSE = "okhttp3.Response";
        public static final String OKHTTP3_REQUEST_BODY = "okhttp3.RequestBody";
        public static final String OKHTTP3_RESPONSE_BODY = "okhttp3.ResponseBody";
    }
    
    // JSON解析相关类名
    public static final class JsonClasses {
        public static final String GSON = "com.google.gson.Gson";
        public static final String JSON_ELEMENT = "com.google.gson.JsonElement";
        public static final String JSON_OBJECT = "com.google.gson.JsonObject";
        public static final String JSON_ARRAY = "com.google.gson.JsonArray";
    }
    
    // 关键方法名
    public static final class Methods {
        public static final String ON_CREATE = "onCreate";
        public static final String ON_VIEW_CREATED = "onViewCreated";
        public static final String ON_RESUME = "onResume";
        public static final String SET_DATA = "setData";
        public static final String LOAD_DATA = "loadData";
        public static final String FROM_JSON = "fromJson";
        public static final String TO_JSON = "toJson";
        public static final String URL = "url";
        public static final String BODY = "body";
        public static final String STRING = "string";
    }
    
    // 关键字段名
    public static final class Fields {
        public static final String GOODS_ENTITY = "goodsEntity";
        public static final String CATEGORY_DATA = "categoryData";
        public static final String DATA = "data";
        public static final String RESULT = "result";
        public static final String LIST = "list";
        public static final String ITEMS = "items";
    }
    
    // URL模式
    public static final class UrlPatterns {
        public static final String[] GOODS_PATTERNS = {
            "goods", "product", "item", "detail", "sku"
        };
        
        public static final String[] CATEGORY_PATTERNS = {
            "category", "mall", "classification", "type"
        };
        
        public static final String[] API_PATTERNS = {
            "/api/", "/v1/", "/v2/", "/mobile/"
        };
    }
    
    // JSON关键字
    public static final class JsonKeys {
        public static final String[] GOODS_KEYS = {
            "goods_id", "goodsId", "product_id", "productId", "item_id", "itemId",
            "goods_name", "goodsName", "product_name", "productName",
            "price", "original_price", "discount_price",
            "image_url", "imageUrl", "thumb_url", "thumbUrl",
            "description", "detail", "goods_desc"
        };
        
        public static final String[] CATEGORY_KEYS = {
            "category_id", "categoryId", "cat_id", "catId",
            "category_name", "categoryName", "cat_name", "catName",
            "parent_id", "parentId", "level", "children"
        };
    }
}