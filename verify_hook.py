#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼多多Hook效果验证脚本
用于分析Hook日志并验证商品数据捕获效果
"""

import re
import json
import subprocess
import time
from datetime import datetime
from collections import defaultdict

class PDDHookVerifier:
    def __init__(self):
        self.hook_modules = [
            "PDDGoodsDetailHook",
            "PDDJsonDataHook", 
            "PDDEnhancedHook",
            "PDDAdvancedHook"
        ]
        self.goods_data = defaultdict(dict)
        self.json_data = []
        self.api_calls = []
        
    def run_adb_logcat(self, duration=30):
        """运行ADB logcat收集日志"""
        print(f"🔍 开始收集Hook日志，持续{duration}秒...")
        print("请在手机上打开拼多多并浏览商品详情页面")
        
        # 清空logcat缓冲区
        subprocess.run(["adb", "logcat", "-c"], capture_output=True)
        
        # 构建过滤条件
        filter_pattern = "|".join(self.hook_modules)
        
        try:
            # 启动logcat进程
            process = subprocess.Popen(
                ["adb", "logcat"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            start_time = time.time()
            logs = []
            
            while time.time() - start_time < duration:
                line = process.stdout.readline()
                if line:
                    # 检查是否包含我们的Hook模块
                    if any(module in line for module in self.hook_modules):
                        logs.append(line.strip())
                        print(f"📝 {line.strip()}")
                        
            process.terminate()
            return logs
            
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断日志收集")
            process.terminate()
            return logs
        except Exception as e:
            print(f"❌ 日志收集失败: {e}")
            return []
    
    def parse_logs(self, logs):
        """解析Hook日志"""
        print(f"\n📊 开始分析 {len(logs)} 条日志...")
        
        goods_id_pattern = r'goodsId.*?(\d{10,})'
        json_pattern = r'JSON数据: (\{.*?\})'
        api_pattern = r'捕获.*?API: (https?://[^\s]+)'
        
        for log in logs:
            # 提取商品ID
            goods_id_match = re.search(goods_id_pattern, log)
            if goods_id_match:
                goods_id = goods_id_match.group(1)
                self.goods_data[goods_id]['id'] = goods_id
                self.goods_data[goods_id]['timestamp'] = datetime.now().isoformat()
            
            # 提取JSON数据
            json_match = re.search(json_pattern, log)
            if json_match:
                try:
                    json_str = json_match.group(1)
                    json_obj = json.loads(json_str)
                    self.json_data.append({
                        'timestamp': datetime.now().isoformat(),
                        'data': json_obj
                    })
                except json.JSONDecodeError:
                    pass
            
            # 提取API调用
            api_match = re.search(api_pattern, log)
            if api_match:
                api_url = api_match.group(1)
                self.api_calls.append({
                    'timestamp': datetime.now().isoformat(),
                    'url': api_url
                })
            
            # 提取商品字段信息
            if '📋' in log:
                field_match = re.search(r'📋 (\w+): (.+)', log)
                if field_match and self.goods_data:
                    field_name = field_match.group(1)
                    field_value = field_match.group(2)
                    # 将字段添加到最新的商品数据中
                    latest_goods_id = list(self.goods_data.keys())[-1] if self.goods_data else 'unknown'
                    self.goods_data[latest_goods_id][field_name] = field_value
    
    def generate_report(self):
        """生成验证报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'goods_count': len(self.goods_data),
                'json_count': len(self.json_data),
                'api_count': len(self.api_calls)
            },
            'goods_data': dict(self.goods_data),
            'json_data': self.json_data,
            'api_calls': self.api_calls
        }
        
        # 保存详细报告
        with open('hook_verification_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report
    
    def print_summary(self, report):
        """打印验证摘要"""
        print("\n" + "="*60)
        print("🎯 拼多多Hook验证报告")
        print("="*60)
        
        summary = report['summary']
        print(f"📊 统计信息:")
        print(f"   🛍️  捕获商品数量: {summary['goods_count']}")
        print(f"   📄 JSON数据条数: {summary['json_count']}")
        print(f"   🌐 API调用次数: {summary['api_count']}")
        
        if summary['goods_count'] > 0:
            print(f"\n🛍️ 商品详情:")
            for goods_id, data in report['goods_data'].items():
                print(f"   商品ID: {goods_id}")
                for key, value in data.items():
                    if key != 'id':
                        print(f"     {key}: {value}")
                print()
        
        if summary['api_count'] > 0:
            print(f"🌐 API调用:")
            for api in report['api_calls'][-3:]:  # 显示最近3个API调用
                print(f"   {api['url']}")
        
        print(f"\n📄 详细报告已保存到: hook_verification_report.json")
        
        # 评估Hook效果
        self.evaluate_hook_effectiveness(summary)
    
    def evaluate_hook_effectiveness(self, summary):
        """评估Hook效果"""
        print(f"\n🎯 Hook效果评估:")
        
        score = 0
        max_score = 100
        
        # 商品数据捕获 (40分)
        if summary['goods_count'] > 0:
            score += 40
            print(f"   ✅ 商品数据捕获: 成功 (+40分)")
        else:
            print(f"   ❌ 商品数据捕获: 失败 (+0分)")
        
        # JSON数据捕获 (30分)
        if summary['json_count'] > 0:
            score += 30
            print(f"   ✅ JSON数据捕获: 成功 (+30分)")
        else:
            print(f"   ❌ JSON数据捕获: 失败 (+0分)")
        
        # API调用捕获 (30分)
        if summary['api_count'] > 0:
            score += 30
            print(f"   ✅ API调用捕获: 成功 (+30分)")
        else:
            print(f"   ❌ API调用捕获: 失败 (+0分)")
        
        print(f"\n📊 总体评分: {score}/{max_score} ({score}%)")
        
        if score >= 80:
            print(f"🎉 Hook效果优秀！")
        elif score >= 60:
            print(f"👍 Hook效果良好")
        elif score >= 40:
            print(f"⚠️  Hook效果一般，需要优化")
        else:
            print(f"❌ Hook效果较差，需要检查配置")
    
    def run_verification(self, duration=30):
        """运行完整的验证流程"""
        print("🚀 开始拼多多Hook效果验证")
        print("="*60)
        
        # 检查ADB连接
        try:
            result = subprocess.run(["adb", "devices"], capture_output=True, text=True)
            if "device" not in result.stdout:
                print("❌ 未检测到Android设备，请确保ADB连接正常")
                return
        except FileNotFoundError:
            print("❌ 未找到ADB工具，请确保Android SDK已正确安装")
            return
        
        # 收集日志
        logs = self.run_adb_logcat(duration)
        
        if not logs:
            print("⚠️  未收集到Hook日志，请检查：")
            print("   1. Xposed模块是否已启用")
            print("   2. 拼多多应用是否已重启")
            print("   3. 是否已浏览商品详情页面")
            return
        
        # 解析日志
        self.parse_logs(logs)
        
        # 生成报告
        report = self.generate_report()
        
        # 打印摘要
        self.print_summary(report)

def main():
    verifier = PDDHookVerifier()
    
    print("请选择验证模式:")
    print("1. 快速验证 (15秒)")
    print("2. 标准验证 (30秒)")
    print("3. 深度验证 (60秒)")
    print("4. 自定义时长")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    duration_map = {
        '1': 15,
        '2': 30,
        '3': 60
    }
    
    if choice in duration_map:
        duration = duration_map[choice]
    elif choice == '4':
        try:
            duration = int(input("请输入验证时长(秒): "))
        except ValueError:
            duration = 30
    else:
        duration = 30
    
    verifier.run_verification(duration)

if __name__ == "__main__":
    main()
